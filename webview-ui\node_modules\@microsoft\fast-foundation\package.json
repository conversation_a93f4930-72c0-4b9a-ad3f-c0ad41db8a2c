{"name": "@microsoft/fast-foundation", "description": "A library of Web Component building blocks", "sideEffects": false, "version": "2.50.0", "author": {"name": "Microsoft", "url": "https://discord.gg/FcSNfg4"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/fast.git"}, "bugs": {"url": "https://github.com/Microsoft/fast/issues/new/choose"}, "main": "dist/esm/index.js", "types": "dist/fast-foundation.d.ts", "unpkg": "dist/fast-foundation.min.js", "type": "module", "scripts": {"clean:dist": "node ../../../build/clean.js dist", "doc": "api-extractor run --local", "doc:ci": "api-extractor run", "build:rollup": "rollup -c", "build:tsc": "tsc -p ./tsconfig.json", "build": "yarn build:tsc && yarn build:rollup && yarn doc && yarn cem", "dev": "tsc -p ./tsconfig.json -w", "cem": "yarn cem-analyze && yarn cem-markdown", "cem-analyze": "cem analyze", "cem-markdown": "node CEMToMarkdown.mjs", "tdd": "yarn dev & yarn test-chrome:watch", "tdd:firefox": "yarn dev & yarn test-firefox:watch", "prepare": "yarn clean:dist && yarn build", "prettier": "prettier --config ../../../.prettierrc --write \"**/*.ts\"", "prettier:diff": "prettier --config ../../../.prettierrc \"**/*.ts\" --list-different", "eslint": "eslint . --ext .ts", "eslint:fix": "eslint . --ext .ts --fix", "test": "yarn eslint && yarn test-chrome:verbose && yarn doc:ci", "test-node": "mocha --reporter min --exit dist/esm/__test__/setup-node.js './dist/esm/**/*.spec.js'", "test-node:verbose": "mocha --reporter spec --exit dist/esm/__test__/setup-node.js './dist/esm/**/*.spec.js'", "test-chrome": "karma start karma.conf.cjs --browsers=ChromeHeadlessOpt --single-run --coverage", "test-chrome:verbose": "karma start karma.conf.cjs --browsers=ChromeHeadlessOpt --single-run --coverage --reporter=mocha", "test-chrome:watch": "karma start karma.conf.cjs --browsers=ChromeHeadlessOpt --coverage --watch-extensions js", "test-chrome:debugger": "karma start karma.conf.cjs --browsers=ChromeDebugging", "test-chrome:verbose:watch": "karma start karma.conf.cjs --browsers=ChromeHeadlessOpt --coverage --watch-extensions js --reporter=mocha", "test-chrome:verbose:debugger": "karma start karma.conf.cjs --browsers=ChromeDebugging --reporter=mocha", "test-firefox": "karma start karma.conf.cjs --browsers=FirefoxHeadless --single-run --coverage", "test-firefox:verbose": "karma start karma.conf.cjs --browsers=FirefoxHeadless --single-run --coverage --reporter=mocha", "test-firefox:watch": "karma start karma.conf.cjs --browsers=FirefoxHeadless   --coverage --watch-extensions js"}, "devDependencies": {"@custom-elements-manifest/analyzer": "^0.5.7", "@custom-elements-manifest/to-markdown": "^0.1.0", "@microsoft/api-extractor": "7.23.1", "@microsoft/tsdoc-config": "^0.13.4", "@types/chai": "^4.2.11", "@types/karma": "^5.0.0", "@types/mocha": "^7.0.2", "@types/webpack-env": "^1.15.2", "chai": "^4.2.0", "chai-spies": "^1.0.0", "esm": "^3.2.25", "ignore-loader": "^0.1.2", "istanbul": "^0.4.5", "istanbul-instrumenter-loader": "^3.0.1", "jsdom": "^16.2.2", "jsdom-global": "3.0.2", "karma": "^5.0.4", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.2", "karma-coverage-istanbul-reporter": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-source-map-support": "^1.4.0", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "^4.0.2", "mocha": "^7.1.2", "prettier": "2.0.2", "rollup": "^2.7.6", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-filesize": "^9.1.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-transform-tagged-template": "^0.0.3", "rollup-plugin-typescript2": "^0.27.0", "source-map": "^0.7.3", "source-map-loader": "^0.2.4", "ts-loader": "^7.0.2", "ts-node": "^8.9.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.6.2", "webpack": "^4.44.0"}, "dependencies": {"@microsoft/fast-element": "^1.14.0", "@microsoft/fast-web-utilities": "^5.4.1", "tabbable": "^5.2.0", "tslib": "^1.13.0"}, "customElements": "dist/custom-elements.json"}