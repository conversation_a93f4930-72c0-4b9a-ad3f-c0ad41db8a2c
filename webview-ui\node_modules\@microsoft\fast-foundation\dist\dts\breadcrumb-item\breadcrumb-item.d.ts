import { SyntheticViewTemplate } from "@microsoft/fast-element";
import type { FoundationElementDefinition } from "../foundation-element/foundation-element.js";
import { Anchor, DelegatesARIALink } from "../anchor/anchor.js";
import { StartEnd, StartEndOptions } from "../patterns/index.js";
/**
 * Breadcrumb Item configuration options
 *
 * @slot - The default slot for when no href is provided or for providing your own custom elements
 * @slot separator - The slot for providing a custom separator
 * @csspart listitem - The wrapping container for the item, represents a semantic listitem
 * @csspart separator - The wrapping element for the separator
 *
 * @public
 */
export declare type BreadcrumbItemOptions = FoundationElementDefinition & StartEndOptions & {
    separator?: string | SyntheticViewTemplate;
};
/**
 * A Breadcrumb Item Custom HTML Element.
 *
 * @public
 */
export declare class BreadcrumbItem extends Anchor {
    /**
     * @internal
     */
    separator: boolean;
}
/**
 * Mark internal because exporting class and interface of the same name
 * confuses API documenter.
 * TODO: https://github.com/microsoft/fast/issues/3317
 * @internal
 */
export interface BreadcrumbItem extends StartEnd, DelegatesARIALink {
}
