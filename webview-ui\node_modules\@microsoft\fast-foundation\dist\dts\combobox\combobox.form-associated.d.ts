import { FormAssociated } from "../form-associated/form-associated.js";
import { Listbox } from "../listbox/listbox.js";
declare class _Combobox extends Listbox {
}
interface _Combobox extends FormAssociated {
}
declare const FormAssociatedCombobox_base: typeof _Combobox;
/**
 * A form-associated base class for the {@link (Combobox:class)} component.
 *
 * @internal
 */
export declare class FormAssociated<PERSON>ombobox extends FormAssociatedCombobox_base {
    proxy: HTMLInputElement;
}
export {};
