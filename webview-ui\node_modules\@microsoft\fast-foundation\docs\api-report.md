## API Report File for "@microsoft/fast-foundation"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import type { AttributeConfiguration } from '@microsoft/fast-element';
import { Behavior } from '@microsoft/fast-element';
import type { CaptureType } from '@microsoft/fast-element';
import { ComposableStyles } from '@microsoft/fast-element';
import { Constructable } from '@microsoft/fast-element';
import { CSSDirective } from '@microsoft/fast-element';
import { Direction } from '@microsoft/fast-web-utilities';
import { ElementStyles } from '@microsoft/fast-element';
import { ElementViewTemplate } from '@microsoft/fast-element';
import { FASTElement } from '@microsoft/fast-element';
import { Orientation } from '@microsoft/fast-web-utilities';
import type { PartialFASTElementDefinition } from '@microsoft/fast-element';
import { SyntheticViewTemplate } from '@microsoft/fast-element';
import { ViewTemplate } from '@microsoft/fast-element';

// @public
export class Accordion extends FoundationElement {
    // @internal (undocumented)
    accordionItems: HTMLElement[];
    // @internal (undocumented)
    accordionItemsChanged(oldValue: HTMLElement[], newValue: HTMLElement[]): void;
    expandmode: AccordionExpandMode;
}

// @public
export const AccordionExpandMode: {
    readonly single: "single";
    readonly multi: "multi";
};

// @public
export type AccordionExpandMode = typeof AccordionExpandMode[keyof typeof AccordionExpandMode];

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "AccordionItem" because one of its declarations is marked as @internal
//
// @public
export class AccordionItem extends FoundationElement {
    // @internal (undocumented)
    clickHandler: (e: MouseEvent) => void;
    // @internal (undocumented)
    expandbutton: HTMLElement;
    expanded: boolean;
    headinglevel: 1 | 2 | 3 | 4 | 5 | 6;
    id: string;
}

// @internal
export interface AccordionItem extends StartEnd {
}

// @public
export type AccordionItemOptions = FoundationElementDefinition & StartEndOptions & {
    expandedIcon?: string | SyntheticViewTemplate;
    collapsedIcon?: string | SyntheticViewTemplate;
};

// @public
export const accordionItemTemplate: FoundationElementTemplate<ViewTemplate<AccordionItem>, AccordionItemOptions>;

// @public
export const accordionTemplate: FoundationElementTemplate<ViewTemplate<Accordion>>;

// @public
export const all: (key: any, searchAncestors?: boolean | undefined) => ReturnType<typeof DI.inject>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Anchor" because one of its declarations is marked as @internal
//
// @public
export class Anchor extends FoundationElement {
    // @internal (undocumented)
    connectedCallback(): void;
    control: HTMLAnchorElement | undefined;
    // @internal
    defaultSlottedContent: HTMLElement[];
    download: string;
    href: string;
    hreflang: string;
    ping: string;
    referrerpolicy: string;
    rel: string;
    target: "_self" | "_blank" | "_parent" | "_top";
    type: string;
}

// @internal
export interface Anchor extends StartEnd, DelegatesARIALink {
}

// @public
export class AnchoredRegion extends FoundationElement {
    // @internal (undocumented)
    adoptedCallback(): void;
    anchor: string;
    anchorElement: HTMLElement | null;
    autoUpdateMode: AutoUpdateMode;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal (undocumented)
    disconnectedCallback(): void;
    fixedPlacement: boolean;
    horizontalDefaultPosition: HorizontalPosition;
    horizontalInset: boolean;
    horizontalPosition: AnchoredRegionPositionLabel | undefined;
    horizontalPositioningMode: AxisPositioningMode;
    horizontalScaling: AxisScalingMode;
    horizontalThreshold: number;
    horizontalViewportLock: boolean;
    // @internal
    initialLayoutComplete: boolean;
    update: () => void;
    verticalDefaultPosition: VerticalPosition;
    verticalInset: boolean;
    verticalPosition: AnchoredRegionPositionLabel | undefined;
    verticalPositioningMode: AxisPositioningMode;
    verticalScaling: AxisScalingMode;
    verticalThreshold: number;
    verticalViewportLock: boolean;
    viewport: string;
    viewportElement: HTMLElement | null;
}

// @public
export interface AnchoredRegionConfig {
    readonly autoUpdateMode?: AutoUpdateMode;
    readonly fixedPlacement?: boolean;
    readonly horizontalDefaultPosition?: HorizontalPosition;
    readonly horizontalInset?: boolean;
    readonly horizontalPositioningMode?: AxisPositioningMode;
    readonly horizontalScaling?: AxisScalingMode;
    readonly horizontalThreshold?: number;
    readonly horizontalViewportLock?: boolean;
    readonly verticalDefaultPosition?: VerticalPosition;
    readonly verticalInset?: boolean;
    readonly verticalPositioningMode?: AxisPositioningMode;
    readonly verticalScaling?: AxisScalingMode;
    readonly verticalThreshold?: number;
    readonly verticalViewportLock?: boolean;
}

// @public
export type AnchoredRegionPositionLabel = "start" | "insetStart" | "insetEnd" | "end" | "center";

// @public
export const anchoredRegionTemplate: FoundationElementTemplate<ViewTemplate<AnchoredRegion>>;

// @public
export type AnchorOptions = FoundationElementDefinition & StartEndOptions;

// @public
export const anchorTemplate: FoundationElementTemplate<ViewTemplate<Anchor>, AnchorOptions>;

// @public
export function applyMixins(derivedCtor: any, ...baseCtors: any[]): void;

// @public
export class ARIAGlobalStatesAndProperties {
    ariaAtomic: "true" | "false" | string | null;
    ariaBusy: "true" | "false" | string | null;
    ariaControls: string | null;
    ariaCurrent: "page" | "step" | "location" | "date" | "time" | "true" | "false" | string | null;
    ariaDescribedby: string | null;
    ariaDetails: string | null;
    ariaDisabled: "true" | "false" | string | null;
    ariaErrormessage: string | null;
    ariaFlowto: string | null;
    ariaHaspopup: "false" | "true" | "menu" | "listbox" | "tree" | "grid" | "dialog" | string | null;
    ariaHidden: "false" | "true" | string | null;
    ariaInvalid: "false" | "true" | "grammar" | "spelling" | string | null;
    ariaKeyshortcuts: string | null;
    ariaLabel: string | null;
    ariaLabelledby: string | null;
    ariaLive: "assertive" | "off" | "polite" | string | null;
    ariaOwns: string | null;
    ariaRelevant: "additions" | "additions text" | "all" | "removals" | "text" | string | null;
    ariaRoledescription: string | null;
}

// @public
export type AutoUpdateMode = "anchor" | "auto";

// @public
export class Avatar extends FoundationElement {
    color: string;
    connectedCallback(): void;
    fill: string;
    link: string;
    // Warning: (ae-forgotten-export) The symbol "AvatarShape" needs to be exported by the entry point index.d.ts
    shape: AvatarShape;
}

// @public
export type AvatarOptions = FoundationElementDefinition & {
    media?: string | SyntheticViewTemplate;
};

// @public
export const avatarTemplate: FoundationElementTemplate<ViewTemplate<Avatar>, AvatarOptions>;

// @public
export type AxisPositioningMode = "uncontrolled" | "locktodefault" | "dynamic";

// @public
export type AxisScalingMode = "anchor" | "fill" | "content";

// @public
export class Badge extends FoundationElement {
    circular: boolean;
    color: string;
    fill: string;
    // (undocumented)
    generateBadgeStyle: () => string | undefined;
}

// @public
export const badgeTemplate: FoundationElementTemplate<ViewTemplate<Badge>>;

// @public
export class BaseProgress extends FoundationElement {
    // @internal (undocumented)
    connectedCallback(): void;
    max: number;
    min: number;
    paused: boolean;
    // @internal
    percentComplete: number;
    value: number | null;
}

// @public
export class Breadcrumb extends FoundationElement {
    // @internal (undocumented)
    slottedBreadcrumbItems: HTMLElement[];
    // (undocumented)
    slottedBreadcrumbItemsChanged(): void;
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "BreadcrumbItem" because one of its declarations is marked as @internal
//
// @public
export class BreadcrumbItem extends Anchor {
    // @internal (undocumented)
    separator: boolean;
}

// @internal
export interface BreadcrumbItem extends StartEnd, DelegatesARIALink {
}

// @public
export type BreadcrumbItemOptions = FoundationElementDefinition & StartEndOptions & {
    separator?: string | SyntheticViewTemplate;
};

// @public
export const breadcrumbItemTemplate: FoundationElementTemplate<ViewTemplate<BreadcrumbItem>, BreadcrumbItemOptions>;

// @public
export const breadcrumbTemplate: FoundationElementTemplate<ViewTemplate<Breadcrumb>>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-forgotten-export) The symbol "FormAssociatedButton" needs to be exported by the entry point index.d.ts
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Button" because one of its declarations is marked as @internal
//
// @public
export class Button extends FormAssociatedButton {
    autofocus: boolean;
    // @internal (undocumented)
    connectedCallback(): void;
    // (undocumented)
    control: HTMLButtonElement;
    defaultSlottedContent: HTMLElement[];
    // @internal (undocumented)
    disconnectedCallback(): void;
    formaction: string;
    formenctype: string;
    formId: string;
    formmethod: string;
    formnovalidate: boolean;
    formtarget: "_self" | "_blank" | "_parent" | "_top";
    type: "submit" | "reset" | "button";
    validate(): void;
}

// @internal
export interface Button extends StartEnd, DelegatesARIAButton {
}

// @public
export type ButtonOptions = FoundationElementDefinition & StartEndOptions;

// @public
export const buttonTemplate: FoundationElementTemplate<ViewTemplate<Button>, ButtonOptions>;

// @public
export class Calendar extends FoundationElement {
    dateFormatter: DateFormatter;
    dateInString(date: Date | string, datesString: string): boolean;
    dayFormat: DayFormat;
    disabledDates: string;
    getDayClassNames(date: CalendarDateInfo, todayString?: string): string;
    getDays(info?: CalendarInfo, minWeeks?: number): CalendarDateInfo[][];
    getMonthInfo(month?: number, year?: number): CalendarInfo;
    getWeekdayText(): {
        text: string;
        abbr?: string;
    }[];
    handleDateSelect(event: Event, day: CalendarDateInfo): void;
    handleKeydown(event: KeyboardEvent, date: CalendarDateInfo): boolean;
    locale: string;
    minWeeks: number;
    month: number;
    monthFormat: MonthFormat;
    readonly: boolean;
    selectedDates: string;
    weekdayFormat: WeekdayFormat;
    year: number;
    yearFormat: YearFormat;
}

// @public
export const calendarCellTemplate: (context: ElementDefinitionContext, todayString: string) => ViewTemplate<CalendarDateInfo>;

// @public
export type CalendarDateInfo = {
    day: number;
    month: number;
    year: number;
    disabled?: boolean;
    selected?: boolean;
};

// @public
export type CalendarInfo = MonthInfo & {
    previous: MonthInfo;
    next: MonthInfo;
};

// @public
export type CalendarOptions = FoundationElementDefinition & StartEndOptions & {
    title?: FoundationElementTemplate<SyntheticViewTemplate<any, Calendar>, CalendarOptions> | SyntheticViewTemplate | string;
};

// @public (undocumented)
export const calendarRowTemplate: (context: ElementDefinitionContext, todayString: string) => ViewTemplate;

// @public
export const calendarTemplate: FoundationElementTemplate<ViewTemplate<Calendar>, CalendarOptions>;

// @public
export const CalendarTitleTemplate: ViewTemplate<Calendar>;

// @public
export const calendarWeekdayTemplate: (context: ElementDefinitionContext) => ViewTemplate;

// @public
export class Card extends FoundationElement {
}

// @public
export const cardTemplate: FoundationElementTemplate<ViewTemplate<Card>>;

// @alpha (undocumented)
export function CheckableFormAssociated<T extends ConstructableFormAssociated>(BaseCtor: T): T;

// @alpha
export interface CheckableFormAssociated extends FormAssociated {
    // (undocumented)
    checked: boolean;
    // (undocumented)
    checkedAttribute: boolean;
    // (undocumented)
    checkedChanged(oldValue: boolean | undefined, newValue: boolean): void;
    // (undocumented)
    currentChecked: boolean;
    // (undocumented)
    defaultChecked: boolean;
    // (undocumented)
    defaultCheckedChanged(oldValue: boolean | undefined, newValue: boolean): void;
    // (undocumented)
    dirtyChecked: boolean;
}

// @alpha
export type CheckableFormAssociatedElement = FormAssociatedElement & CheckableFormAssociated & {
    proxy: HTMLInputElement;
};

// Warning: (ae-forgotten-export) The symbol "FormAssociatedCheckbox" needs to be exported by the entry point index.d.ts
//
// @public
export class Checkbox extends FormAssociatedCheckbox {
    constructor();
    // @internal (undocumented)
    clickHandler: (e: MouseEvent) => void;
    // @internal (undocumented)
    defaultSlottedNodes: Node[];
    indeterminate: boolean;
    // @internal
    initialValue: string;
    // @internal (undocumented)
    keypressHandler: (e: KeyboardEvent) => void;
    readOnly: boolean;
}

// @public
export type CheckboxOptions = FoundationElementDefinition & {
    checkedIndicator?: string | SyntheticViewTemplate;
    indeterminateIndicator?: string | SyntheticViewTemplate;
};

// @public
export const checkboxTemplate: FoundationElementTemplate<ViewTemplate<Checkbox>, CheckboxOptions>;

// @public
export interface ColumnDefinition {
    cellFocusTargetCallback?: (cell: DataGridCell) => HTMLElement;
    cellInternalFocusQueue?: boolean;
    cellTemplate?: ViewTemplate;
    columnDataKey: string;
    gridColumn?: string;
    headerCellFocusTargetCallback?: (cell: DataGridCell) => HTMLElement;
    headerCellInternalFocusQueue?: boolean;
    headerCellTemplate?: ViewTemplate;
    isRowHeader?: boolean;
    title?: string;
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-forgotten-export) The symbol "FormAssociatedCombobox" needs to be exported by the entry point index.d.ts
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Combobox" because one of its declarations is marked as @internal
//
// @public
export class Combobox extends FormAssociatedCombobox {
    autocomplete: ComboboxAutocomplete | undefined;
    // @internal
    clickHandler(e: MouseEvent): boolean | void;
    // (undocumented)
    connectedCallback(): void;
    // @internal
    control: HTMLInputElement;
    // @internal
    disabledChanged(prev: boolean, next: boolean): void;
    filteredOptions: ListboxOption[];
    filterOptions(): void;
    // @internal
    protected focusAndScrollOptionIntoView(): void;
    // @internal
    focusoutHandler(e: FocusEvent): boolean | void;
    // @internal
    formResetCallback(): void;
    // @internal
    inputHandler(e: InputEvent): boolean | void;
    // @internal
    keydownHandler(e: Event & KeyboardEvent): boolean | void;
    // @internal
    keyupHandler(e: KeyboardEvent): boolean | void;
    // @internal
    listbox: HTMLDivElement;
    // @internal
    listboxId: string;
    // @internal
    maxHeight: number;
    open: boolean;
    // @internal
    protected openChanged(): void;
    get options(): ListboxOption[];
    set options(value: ListboxOption[]);
    placeholder: string;
    // @internal
    protected placeholderChanged(): void;
    position?: SelectPosition;
    positionAttribute?: SelectPosition;
    // (undocumented)
    protected positionChanged(prev: SelectPosition | undefined, next: SelectPosition | undefined): void;
    // @internal
    selectedIndexChanged(prev: number | undefined, next: number): void;
    // @internal
    selectedOptionsChanged(prev: ListboxOption[] | undefined, next: ListboxOption[]): void;
    // @internal
    selectPreviousOption(): void;
    // @internal
    setDefaultSelectedOption(): void;
    setPositioning(): void;
    // @internal
    slottedOptionsChanged(prev: Element[] | undefined, next: Element[]): void;
    validate(): void;
    get value(): string;
    set value(next: string);
}

// @internal
export interface Combobox extends StartEnd, DelegatesARIACombobox {
}

// @public
export const ComboboxAutocomplete: {
    readonly inline: "inline";
    readonly list: "list";
    readonly both: "both";
    readonly none: "none";
};

// @public
export type ComboboxAutocomplete = typeof ComboboxAutocomplete[keyof typeof ComboboxAutocomplete];

// @public
export type ComboboxOptions = FoundationElementDefinition & StartEndOptions & {
    indicator?: string | SyntheticViewTemplate;
};

// @public
export const comboboxTemplate: FoundationElementTemplate<ViewTemplate<Combobox>, ComboboxOptions>;

// @public
export interface ComponentPresentation {
    applyTo(element: FASTElement): void;
}

// @public
export const ComponentPresentation: Readonly<{
    define(tagName: string, presentation: ComponentPresentation, container: Container): void;
    forTag(tagName: string, element: HTMLElement): ComponentPresentation | null;
}>;

// @public
export function composedContains(reference: HTMLElement, test: HTMLElement): boolean;

// @public
export function composedParent<T extends HTMLElement>(element: T): HTMLElement | null;

// @alpha
export type ConstructableFormAssociated = Constructable<HTMLElement & FASTElement>;

// @public
export interface Container extends ServiceLocator {
    createChild(config?: Partial<Omit<ContainerConfiguration, "parentLocator">>): Container;
    getFactory<T extends Constructable>(key: T): Factory<T>;
    getResolver<K extends Key, T = K>(key: K | Key, autoRegister?: boolean): Resolver<T> | null;
    register(...params: any[]): Container;
    registerFactory<T extends Constructable>(key: T, factory: Factory<T>): void;
    registerResolver<K extends Key, T = K>(key: K, resolver: Resolver<T>): Resolver<T>;
    registerTransformer<K extends Key, T = K>(key: K, transformer: Transformer_2<T>): boolean;
    registerWithContext(context: any, ...params: any[]): Container;
}

// @public
export const Container: InterfaceSymbol<Container>;

// @public
export interface ContainerConfiguration {
    defaultResolver(key: Key, handler: Container): Resolver;
    parentLocator: ParentLocator;
    responsibleForOwnerRequests: boolean;
}

// @public
export const ContainerConfiguration: Readonly<{
    default: Readonly<ContainerConfiguration>;
}>;

// Warning: (ae-internal-missing-underscore) The name "ContainerImpl" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal (undocumented)
export class ContainerImpl implements Container {
    constructor(owner: any, config: ContainerConfiguration);
    // (undocumented)
    protected config: ContainerConfiguration;
    // (undocumented)
    createChild(config?: Partial<Omit<ContainerConfiguration, "parentLocator">>): Container;
    // (undocumented)
    get depth(): number;
    // (undocumented)
    get<K extends Key>(key: K): Resolved<K>;
    // (undocumented)
    getAll<K extends Key>(key: K, searchAncestors?: boolean): readonly Resolved<K>[];
    // (undocumented)
    getFactory<K extends Constructable>(Type: K): Factory<K>;
    // (undocumented)
    getResolver<K extends Key, T = K>(key: K | Key, autoRegister?: boolean): Resolver<T> | null;
    // (undocumented)
    has<K extends Key>(key: K, searchAncestors?: boolean): boolean;
    // (undocumented)
    protected owner: any;
    // (undocumented)
    get parent(): ContainerImpl | null;
    // (undocumented)
    register(...params: any[]): Container;
    // (undocumented)
    registerFactory<K extends Constructable>(key: K, factory: Factory<K>): void;
    // (undocumented)
    registerResolver<K extends Key, T = K>(key: K, resolver: Resolver<T>): Resolver<T>;
    // (undocumented)
    registerTransformer<K extends Key, T = K>(key: K, transformer: Transformer_2<T>): boolean;
    // (undocumented)
    registerWithContext(context: any, ...params: any[]): Container;
    // (undocumented)
    get responsibleForOwnerRequests(): boolean;
}

// @public
export type ContextualElementDefinition = Omit<PartialFASTElementDefinition, "name">;

// @public
export interface CSSDesignToken<T extends string | number | boolean | BigInteger | null | Array<any> | symbol | ({
    createCSS?(): string;
} & Record<PropertyKey, any>)> extends DesignToken<T>, CSSDirective {
    readonly cssCustomProperty: string;
}

// @public
export type CSSDisplayPropertyValue = "block" | "contents" | "flex" | "grid" | "inherit" | "initial" | "inline" | "inline-block" | "inline-flex" | "inline-grid" | "inline-table" | "list-item" | "none" | "run-in" | "table" | "table-caption" | "table-cell" | "table-column" | "table-column-group" | "table-footer-group" | "table-header-group" | "table-row" | "table-row-group";

// @public
export const darkModeStylesheetBehavior: (styles: ElementStyles) => MatchMediaStyleSheetBehavior;

// @public
export class DataGrid extends FoundationElement {
    constructor();
    cellItemTemplate?: ViewTemplate;
    columnDefinitions: ColumnDefinition[] | null;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal
    defaultRowItemTemplate: ViewTemplate;
    // @internal (undocumented)
    disconnectedCallback(): void;
    focusColumnIndex: number;
    focusRowIndex: number;
    static generateColumns: (row: object) => ColumnDefinition[];
    generateHeader: GenerateHeaderOptions;
    gridTemplateColumns: string;
    // @internal (undocumented)
    handleFocus(e: FocusEvent): void;
    // @internal (undocumented)
    handleFocusOut(e: FocusEvent): void;
    // @internal (undocumented)
    handleKeydown(e: KeyboardEvent): void;
    // @internal (undocumented)
    handleRowFocus(e: Event): void;
    headerCellItemTemplate?: ViewTemplate;
    noTabbing: boolean;
    // @internal
    rowElements: HTMLElement[];
    rowElementTag: string;
    rowItemTemplate: ViewTemplate;
    rowsData: object[];
}

// @public
export class DataGridCell extends FoundationElement {
    cellType: DataGridCellTypes;
    columnDefinition: ColumnDefinition | null;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal (undocumented)
    disconnectedCallback(): void;
    gridColumn: string;
    // (undocumented)
    handleFocusin(e: FocusEvent): void;
    // (undocumented)
    handleFocusout(e: FocusEvent): void;
    // (undocumented)
    handleKeydown(e: KeyboardEvent): void;
    rowData: object | null;
}

// @public
export const dataGridCellTemplate: FoundationElementTemplate<ViewTemplate<DataGridCell>>;

// @public
export const DataGridCellTypes: {
    readonly default: "default";
    readonly columnHeader: "columnheader";
    readonly rowHeader: "rowheader";
};

// @public
export type DataGridCellTypes = typeof DataGridCellTypes[keyof typeof DataGridCellTypes];

// @public
export class DataGridRow extends FoundationElement {
    // @internal
    activeCellItemTemplate?: ViewTemplate;
    // @internal
    cellElements: HTMLElement[];
    cellItemTemplate?: ViewTemplate;
    columnDefinitions: ColumnDefinition[] | null;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal
    defaultCellItemTemplate?: ViewTemplate;
    // @internal
    defaultHeaderCellItemTemplate?: ViewTemplate;
    // @internal (undocumented)
    disconnectedCallback(): void;
    // @internal (undocumented)
    focusColumnIndex: number;
    gridTemplateColumns: string;
    // (undocumented)
    handleCellFocus(e: Event): void;
    // (undocumented)
    handleFocusout(e: FocusEvent): void;
    // (undocumented)
    handleKeydown(e: KeyboardEvent): void;
    headerCellItemTemplate?: ViewTemplate;
    // @internal
    isActiveRow: boolean;
    rowData: object | null;
    rowIndex: number;
    rowType: DataGridRowTypes;
    // @internal (undocumented)
    slottedCellElements: HTMLElement[];
}

// @public
export const dataGridRowTemplate: FoundationElementTemplate<ViewTemplate<DataGridRow>>;

// @public
export const DataGridRowTypes: {
    readonly default: "default";
    readonly header: "header";
    readonly stickyHeader: "sticky-header";
};

// @public
export type DataGridRowTypes = typeof DataGridRowTypes[keyof typeof DataGridRowTypes];

// @public
export const dataGridTemplate: FoundationElementTemplate<ViewTemplate<DataGrid>>;

// @public
export class DateFormatter {
    constructor(config?: {});
    date: Date;
    dayFormat: DayFormat;
    // (undocumented)
    getDate(date?: {
        day: number;
        month: number;
        year: number;
    } | string | Date, format?: Intl.DateTimeFormatOptions, locale?: string): string;
    getDateObject(date: {
        day: number;
        month: number;
        year: number;
    } | string | Date): Date;
    // (undocumented)
    getDay(day?: number, format?: DayFormat, locale?: string): string;
    // (undocumented)
    getMonth(month?: number, format?: MonthFormat, locale?: string): string;
    // (undocumented)
    getWeekday(weekday?: number, format?: WeekdayFormat, locale?: string): string;
    // (undocumented)
    getWeekdays(format?: WeekdayFormat, locale?: string): string[];
    // (undocumented)
    getYear(year?: number, format?: YearFormat, locale?: string): string;
    locale: string;
    monthFormat: MonthFormat;
    weekdayFormat: WeekdayFormat;
    yearFormat: YearFormat;
}

// @public
export type DayFormat = "2-digit" | "numeric";

// @public
export class DefaultComponentPresentation implements ComponentPresentation {
    constructor(template?: ElementViewTemplate, styles?: ComposableStyles | ComposableStyles[]);
    applyTo(element: FASTElement): void;
    readonly styles: ElementStyles | null;
    readonly template: ElementViewTemplate | null;
}

// @public
export const DefaultResolver: Readonly<{
    none(key: Key): Resolver;
    singleton(key: Key): Resolver;
    transient(key: Key): Resolver;
}>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIAButton" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIAButton {
    ariaExpanded: "true" | "false" | string | null;
    ariaPressed: "true" | "false" | "mixed" | string | null;
}

// @internal
export interface DelegatesARIAButton extends ARIAGlobalStatesAndProperties {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIACombobox" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIACombobox {
    ariaAutoComplete: "inline" | "list" | "both" | "none" | string | null;
    ariaControls: string | null;
}

// @internal
export interface DelegatesARIACombobox extends DelegatesARIAListbox {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIALink" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIALink {
    ariaExpanded: "true" | "false" | string | null;
}

// @internal
export interface DelegatesARIALink extends ARIAGlobalStatesAndProperties {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIAListbox" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIAListbox {
    ariaActiveDescendant: string | null;
    ariaDisabled: "true" | "false" | string | null;
    ariaExpanded: "true" | "false" | string | null;
    ariaMultiSelectable: "true" | "false" | string | null;
}

// @internal
export interface DelegatesARIAListbox extends ARIAGlobalStatesAndProperties {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIAListboxOption" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIAListboxOption {
    ariaChecked: "true" | "false" | string | null;
    ariaPosInSet: string | null;
    ariaSelected: "true" | "false" | string | null;
    ariaSetSize: string | null;
}

// @internal (undocumented)
export interface DelegatesARIAListboxOption extends ARIAGlobalStatesAndProperties {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIASearch" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIASearch {
}

// @internal
export interface DelegatesARIASearch extends ARIAGlobalStatesAndProperties {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIASelect" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIASelect {
    ariaControls: string | null;
}

// @internal
export interface DelegatesARIASelect extends DelegatesARIAListbox {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIATextbox" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIATextbox {
}

// @internal
export interface DelegatesARIATextbox extends ARIAGlobalStatesAndProperties {
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "DelegatesARIAToolbar" because one of its declarations is marked as @internal
//
// @public
export class DelegatesARIAToolbar {
    ariaLabel: string | null;
    ariaLabelledby: string | null;
}

// @internal
export interface DelegatesARIAToolbar extends ARIAGlobalStatesAndProperties {
}

// @public
export type DerivedDesignTokenValue<T> = T extends Function ? never : (target: HTMLElement) => T;

// @public
export interface DesignSystem {
    register(...params: any[]): DesignSystem;
    withDesignTokenRoot(root: HTMLElement | Document | null): DesignSystem;
    withElementDisambiguation(callback: ElementDisambiguationCallback): DesignSystem;
    withPrefix(prefix: string): DesignSystem;
    withShadowRootMode(mode: ShadowRootMode): DesignSystem;
}

// @public
export const DesignSystem: Readonly<{
    tagFor(type: Constructable): string;
    responsibleFor(element: HTMLElement): DesignSystem;
    getOrCreate(node?: Node | undefined): DesignSystem;
}>;

// @public
export interface DesignSystemRegistrationContext {
    readonly elementPrefix: string;
    // @deprecated
    tryDefineElement(name: string, type: Constructable, callback: ElementDefinitionCallback): void;
    tryDefineElement(params: ElementDefinitionParams): void;
}

// @public
export interface DesignToken<T extends string | number | boolean | BigInteger | null | Array<any> | symbol | {}> {
    readonly appliedTo: HTMLElement[];
    deleteValueFor(element: HTMLElement): this;
    getValueFor(element: HTMLElement): StaticDesignTokenValue<T>;
    readonly name: string;
    setValueFor(element: HTMLElement, value: DesignTokenValue<T> | DesignToken<T>): void;
    subscribe(subscriber: DesignTokenSubscriber<this>, target?: HTMLElement): void;
    unsubscribe(subscriber: DesignTokenSubscriber<this>, target?: HTMLElement): void;
    withDefault(value: DesignTokenValue<T> | DesignToken<T>): this;
}

// @public
export const DesignToken: Readonly<{
    create: typeof create;
    notifyConnection(element: HTMLElement): boolean;
    notifyDisconnection(element: HTMLElement): boolean;
    registerRoot(target?: HTMLElement | Document): void;
    unregisterRoot(target?: HTMLElement | Document): void;
}>;

// @public
export interface DesignTokenChangeRecord<T extends DesignToken<any>> {
    target: HTMLElement;
    token: T;
}

// @public
export interface DesignTokenConfiguration {
    cssCustomPropertyName?: string | null;
    name: string;
}

// @public
export interface DesignTokenSubscriber<T extends DesignToken<any>> {
    // (undocumented)
    handleChange(record: DesignTokenChangeRecord<T>): void;
}

// @public
export type DesignTokenValue<T> = StaticDesignTokenValue<T> | DerivedDesignTokenValue<T>;

// @public
export const DI: Readonly<{
    createContainer(config?: Partial<ContainerConfiguration> | undefined): Container;
    findResponsibleContainer(node: Node): Container;
    findParentContainer(node: Node): Container;
    getOrCreateDOMContainer(node?: Node | undefined, config?: Partial<Omit<ContainerConfiguration, "parentLocator">> | undefined): Container;
    getDesignParamtypes: (Type: Constructable | Injectable) => readonly Key[] | undefined;
    getAnnotationParamtypes: (Type: Constructable | Injectable) => readonly Key[] | undefined;
    getOrCreateAnnotationParamTypes(Type: Constructable | Injectable): Key[];
    getDependencies(Type: Constructable | Injectable): Key[];
    defineProperty(target: {}, propertyName: string, key: Key, respectConnection?: boolean): void;
    createInterface<K extends Key>(nameConfigOrCallback?: string | InterfaceConfiguration | ((builder: ResolverBuilder<K>) => Resolver<K>) | undefined, configuror?: ((builder: ResolverBuilder<K>) => Resolver<K>) | undefined): InterfaceSymbol<K>;
    inject(...dependencies: Key[]): (target: any, key?: string | number | undefined, descriptor?: number | PropertyDescriptor | undefined) => void;
    transient<T extends Constructable<{}>>(target: T & Partial<RegisterSelf<T>>): T & RegisterSelf<T>;
    singleton<T_1 extends Constructable<{}>>(target: T_1 & Partial<RegisterSelf<T_1>>, options?: SingletonOptions): T_1 & RegisterSelf<T_1>;
}>;

// @public
export class Dialog extends FoundationElement {
    ariaDescribedby: string;
    ariaLabel: string;
    ariaLabelledby: string;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal (undocumented)
    dialog: HTMLDivElement;
    // @internal (undocumented)
    disconnectedCallback(): void;
    // @internal (undocumented)
    dismiss(): void;
    // @internal (undocumented)
    handleChange(source: any, propertyName: string): void;
    hidden: boolean;
    hide(): void;
    modal: boolean;
    show(): void;
    trapFocus: boolean;
}

// @public
export const dialogTemplate: FoundationElementTemplate<ViewTemplate<Dialog>>;

// @public
export const disabledCursor = "not-allowed";

// @public
export class Disclosure extends FoundationElement {
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal (undocumented)
    details: HTMLDetailsElement;
    // @internal (undocumented)
    disconnectedCallback(): void;
    expanded: boolean;
    hide(): void;
    protected onToggle(): void;
    protected setup(): void;
    show(): void;
    title: string;
    toggle(): void;
}

// @public
export const disclosureTemplate: FoundationElementTemplate<ViewTemplate<Disclosure>>;

// @public
export function display(displayValue: CSSDisplayPropertyValue): string;

// @public
export class Divider extends FoundationElement {
    orientation: Orientation;
    role: DividerRole;
}

// @public
export const DividerRole: {
    readonly separator: "separator";
    readonly presentation: "presentation";
};

// @public
export type DividerRole = typeof DividerRole[keyof typeof DividerRole];

// @public
export const dividerTemplate: FoundationElementTemplate<ViewTemplate<Divider>>;

// @public
export type ElementDefinitionCallback = (ctx: ElementDefinitionContext) => void;

// @public
export interface ElementDefinitionContext {
    readonly container: Container;
    defineElement(definition?: ContextualElementDefinition): void;
    definePresentation(presentation: ComponentPresentation): void;
    readonly name: string;
    readonly shadowRootMode: ShadowRootMode | undefined;
    tagFor(type: Constructable): string;
    readonly type: Constructable;
    readonly willDefine: boolean;
}

// @public
export interface ElementDefinitionParams extends Pick<ElementDefinitionContext, "name" | "type"> {
    readonly baseClass?: Constructable;
    callback: ElementDefinitionCallback;
}

// @public
export const ElementDisambiguation: Readonly<{
    definitionCallbackOnly: null;
    ignoreDuplicate: symbol;
}>;

// @public
export type ElementDisambiguationCallback = (nameAttempt: string, typeAttempt: Constructable, existingType: Constructable) => ElementDisambiguationResult;

// @public
export type ElementDisambiguationResult = string | typeof ElementDisambiguation.ignoreDuplicate | typeof ElementDisambiguation.definitionCallbackOnly;

// @public
export type EndOptions = {
    end?: string | SyntheticViewTemplate;
};

// @public
export const endSlotTemplate: (context: ElementDefinitionContext, definition: EndOptions) => ViewTemplate<StartEnd>;

// @public @deprecated
export const endTemplate: ViewTemplate<StartEnd>;

// @public
export interface Factory<T extends Constructable = any> {
    construct(container: Container, dynamicDependencies?: Key[]): Resolved<T>;
    registerTransformer(transformer: Transformer_2<T>): void;
    readonly Type: T;
}

// Warning: (ae-internal-missing-underscore) The name "FactoryImpl" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal (undocumented)
export class FactoryImpl<T extends Constructable = any> implements Factory<T> {
    constructor(Type: T, dependencies: Key[]);
    // (undocumented)
    construct(container: Container, dynamicDependencies?: Key[]): Resolved<T>;
    // (undocumented)
    registerTransformer(transformer: (instance: any) => any): void;
    // (undocumented)
    Type: T;
}

// @public
export class Flipper extends FoundationElement {
    direction: FlipperDirection;
    disabled: boolean;
    hiddenFromAT: boolean;
    keyupHandler(e: Event & KeyboardEvent): void;
}

// @public
export const FlipperDirection: {
    readonly next: "next";
    readonly previous: "previous";
};

// @public
export type FlipperDirection = typeof FlipperDirection[keyof typeof FlipperDirection];

// @public
export type FlipperOptions = FoundationElementDefinition & {
    next?: string | SyntheticViewTemplate;
    previous?: string | SyntheticViewTemplate;
};

// @public
export const flipperTemplate: FoundationElementTemplate<ViewTemplate<Flipper>, FlipperOptions>;

// @public
export const FlyoutPosBottom: AnchoredRegionConfig;

// @public
export const FlyoutPosBottomFill: AnchoredRegionConfig;

// @public
export const FlyoutPosTallest: AnchoredRegionConfig;

// @public
export const FlyoutPosTallestFill: AnchoredRegionConfig;

// @public
export const FlyoutPosTop: AnchoredRegionConfig;

// @public
export const FlyoutPosTopFill: AnchoredRegionConfig;

// @public
export const focusVisible: string;

// @public
export const forcedColorsStylesheetBehavior: (styles: ElementStyles) => MatchMediaStyleSheetBehavior;

// @alpha
export function FormAssociated<T extends ConstructableFormAssociated>(BaseCtor: T): T;

// Warning: (ae-forgotten-export) The symbol "ElementInternals" needs to be exported by the entry point index.d.ts
//
// @alpha
export interface FormAssociated extends Omit<ElementInternals_2, "labels"> {
    // (undocumented)
    attachProxy(): void;
    // (undocumented)
    currentValue: string;
    // (undocumented)
    detachProxy(): void;
    // (undocumented)
    dirtyValue: boolean;
    // (undocumented)
    disabled: boolean;
    // (undocumented)
    disabledChanged?(previous: boolean, next: boolean): void;
    // (undocumented)
    readonly elementInternals: ElementInternals_2 | null;
    // (undocumented)
    readonly formAssociated: boolean;
    // (undocumented)
    formDisabledCallback?(disabled: boolean): void;
    // (undocumented)
    formResetCallback(): void;
    // (undocumented)
    initialValue: string;
    // (undocumented)
    initialValueChanged?(previous: string, next: string): void;
    // (undocumented)
    readonly labels: ReadonlyArray<Node[]>;
    // (undocumented)
    name: string;
    // (undocumented)
    nameChanged?(previous: string, next: string): void;
    // (undocumented)
    required: boolean;
    // (undocumented)
    requiredChanged(prev: boolean, next: boolean): void;
    // (undocumented)
    stopPropagation(e: Event): void;
    validate(anchor?: HTMLElement): void;
    // (undocumented)
    value: string;
    // (undocumented)
    valueChanged(previous: string, next: string): void;
}

// @alpha
export type FormAssociatedElement = FormAssociated & FASTElement & HTMLElement & FormAssociatedProxy;

// @alpha
export interface FormAssociatedProxy {
    // (undocumented)
    disabledChanged?(previous: boolean, next: boolean): void;
    // (undocumented)
    formDisabledCallback?(disabled: boolean): void;
    // (undocumented)
    formResetCallback?(): void;
    // (undocumented)
    initialValueChanged?(previous: string, next: string): void;
    // (undocumented)
    nameChanged?(previous: string, next: string): void;
    // (undocumented)
    proxy: ProxyElement;
    // (undocumented)
    valueChanged?(previous: string, next: string): void;
}

// @public
export class FoundationElement extends FASTElement {
    protected get $presentation(): ComponentPresentation | null;
    // Warning: (ae-incompatible-release-tags) The symbol "compose" is marked as @public, but its signature references "FoundationElementRegistry" which is marked as @internal
    static compose<T extends FoundationElementDefinition = FoundationElementDefinition, K extends Constructable<FoundationElement> = Constructable<FoundationElement>>(this: K, elementDefinition: T): (overrideDefinition?: OverrideFoundationElementDefinition<T>) => FoundationElementRegistry<T, K>;
    connectedCallback(): void;
    styles: ElementStyles | void | null;
    // (undocumented)
    protected stylesChanged(): void;
    template: ElementViewTemplate | void | null;
    // (undocumented)
    protected templateChanged(): void;
}

// @public
export interface FoundationElementDefinition {
    readonly attributes?: EagerOrLazyFoundationOption<(AttributeConfiguration | string)[], this>;
    baseClass?: Constructable;
    baseName: string;
    readonly elementOptions?: EagerOrLazyFoundationOption<ElementDefinitionOptions, this>;
    readonly shadowOptions?: EagerOrLazyFoundationOption<Partial<ShadowRootInit> | null, this>;
    readonly styles?: EagerOrLazyFoundationOption<ComposableStyles | ComposableStyles[], this>;
    // Warning: (ae-forgotten-export) The symbol "EagerOrLazyFoundationOption" needs to be exported by the entry point index.d.ts
    readonly template?: EagerOrLazyFoundationOption<ElementViewTemplate, this>;
}

// Warning: (ae-internal-missing-underscore) The name "FoundationElementRegistry" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal
export class FoundationElementRegistry<TDefinition extends FoundationElementDefinition, TType> implements Registry {
    constructor(type: Constructable<FoundationElement>, elementDefinition: TDefinition, overrideDefinition: OverrideFoundationElementDefinition<TDefinition>);
    // (undocumented)
    readonly definition: OverrideFoundationElementDefinition<TDefinition>;
    // (undocumented)
    register(container: Container, context: DesignSystemRegistrationContext): void;
    // (undocumented)
    readonly type: Constructable<FoundationElement>;
}

// Warning: (ae-forgotten-export) The symbol "LazyFoundationOption" needs to be exported by the entry point index.d.ts
//
// @public
export type FoundationElementTemplate<T, K extends FoundationElementDefinition = FoundationElementDefinition> = LazyFoundationOption<T, K>;

// @public
export const GenerateHeaderOptions: {
    readonly none: "none";
    readonly default: "default";
    readonly sticky: "sticky";
};

// @public
export type GenerateHeaderOptions = typeof GenerateHeaderOptions[keyof typeof GenerateHeaderOptions];

// @public
export const getDirection: (rootNode: HTMLElement) => Direction;

// @public
export const hidden = ":host([hidden]){display:none}";

// @public
export type HorizontalPosition = "start" | "end" | "left" | "right" | "center" | "unset";

// @public
export class HorizontalScroll extends FoundationElement {
    // (undocumented)
    connectedCallback(): void;
    content: HTMLDivElement;
    // (undocumented)
    disconnectedCallback(): void;
    duration: string;
    easing: ScrollEasing;
    flippersHiddenFromAT: boolean;
    keyupHandler(e: Event & KeyboardEvent): void;
    nextFlipperContainer: HTMLDivElement;
    previousFlipperContainer: HTMLDivElement;
    resized(): void;
    scrollContainer: HTMLDivElement;
    scrolled(): void;
    // @internal
    scrollingChanged(prev: unknown, next: boolean): void;
    scrollInView(item: HTMLElement | number, padding?: number, rightPadding?: number): void;
    scrollItems: HTMLElement[];
    scrollItemsChanged(previous: HTMLElement[], next: HTMLElement[]): void;
    scrollToNext(): void;
    scrollToPosition(newPosition: number, position?: number): void;
    scrollToPrevious(): void;
    speed: number;
    view: HorizontalScrollView;
}

// @public
export type HorizontalScrollOptions = FoundationElementDefinition & StartEndOptions & {
    nextFlipper?: FoundationElementTemplate<SyntheticViewTemplate<any, HorizontalScroll>, HorizontalScrollOptions> | SyntheticViewTemplate | string;
    previousFlipper?: FoundationElementTemplate<SyntheticViewTemplate<any, HorizontalScroll>, HorizontalScrollOptions> | SyntheticViewTemplate | string;
};

// @public (undocumented)
export const horizontalScrollTemplate: FoundationElementTemplate<ViewTemplate<HorizontalScroll>, HorizontalScrollOptions>;

// @public
export type HorizontalScrollView = "default" | "mobile";

// @public
export function ignore(target: Injectable, property?: string | number, descriptor?: PropertyDescriptor | number): void;

// @public
export const inject: (...dependencies: Key[]) => (target: any, key?: string | number | undefined, descriptor?: number | PropertyDescriptor | undefined) => void;

// @public
export type Injectable<T = {}> = Constructable<T> & {
    inject?: Key[];
};

// Warning: (ae-internal-missing-underscore) The name "interactiveCalendarGridTemplate" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal
export const interactiveCalendarGridTemplate: (context: ElementDefinitionContext, todayString: string) => ViewTemplate;

// @public
export interface InterfaceConfiguration {
    friendlyName?: string;
    respectConnection?: boolean;
}

// @public
export type InterfaceSymbol<K = any> = (target: any, property: string, index?: number) => void;

// @public
export function isListboxOption(el: Element): el is ListboxOption;

// @public
export function isTreeItemElement(el: Element): el is HTMLElement;

// @public
export type Key = PropertyKey | object | InterfaceSymbol | Constructable | Resolver;

// @public
export const lazy: (key: any) => any;

// @public
export const lightModeStylesheetBehavior: (styles: ElementStyles) => MatchMediaStyleSheetBehavior;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Listbox" because one of its declarations is marked as @internal
//
// @public
export abstract class Listbox extends FoundationElement {
    // @internal
    clickHandler(e: MouseEvent): boolean | void;
    disabled: boolean;
    // @internal
    get firstSelectedOption(): ListboxOption;
    // @internal
    protected focusAndScrollOptionIntoView(optionToFocus?: ListboxOption | null): void;
    // @internal
    focusinHandler(e: FocusEvent): void;
    // @internal
    protected getSelectableIndex(prev: number | undefined, next: number): number;
    // @internal
    protected getTypeaheadMatches(): ListboxOption[];
    // @internal
    handleChange(source: any, propertyName: string): void;
    // @internal
    handleTypeAhead(key: string): void;
    // @internal
    protected get hasSelectableOptions(): boolean;
    // @internal
    keydownHandler(e: KeyboardEvent): boolean | void;
    get length(): number;
    // @internal
    mousedownHandler(e: MouseEvent): boolean | void;
    // @internal
    multipleChanged(prev: boolean | undefined, next: boolean): void;
    get options(): ListboxOption[];
    set options(value: ListboxOption[]);
    // @internal
    protected _options: ListboxOption[];
    selectedIndex: number;
    // @internal
    selectedIndexChanged(prev: number | undefined, next: number): void;
    selectedOptions: ListboxOption[];
    // @internal
    protected selectedOptionsChanged(prev: ListboxOption[] | undefined, next: ListboxOption[]): void;
    selectFirstOption(): void;
    // @internal
    selectLastOption(): void;
    // @internal
    selectNextOption(): void;
    // @internal
    selectPreviousOption(): void;
    // @internal
    protected setDefaultSelectedOption(): void;
    protected setSelectedOptions(): void;
    // @internal
    protected shouldSkipFocus: boolean;
    static slottedOptionFilter: (n: HTMLElement) => boolean;
    // @internal
    slottedOptions: Element[];
    // @internal
    slottedOptionsChanged(prev: Element[] | undefined, next: Element[]): void;
    // @internal
    protected static readonly TYPE_AHEAD_TIMEOUT_MS = 1000;
    // @internal
    protected typeaheadBuffer: string;
    // @internal
    typeaheadBufferChanged(prev: string, next: string): void;
    // @internal @deprecated
    protected get typeAheadExpired(): boolean;
    protected set typeAheadExpired(value: boolean);
    // @internal
    protected typeaheadExpired: boolean;
    // @internal
    protected typeaheadTimeout: number;
}

// @internal (undocumented)
export interface Listbox extends DelegatesARIAListbox {
}

// @public
export class ListboxElement extends Listbox {
    // @internal
    protected activeIndex: number;
    // @internal
    protected activeIndexChanged(prev: number | undefined, next: number): void;
    // @internal
    get activeOption(): ListboxOption | null;
    // @internal
    protected checkActiveIndex(): void;
    // @internal
    protected get checkedOptions(): ListboxOption[];
    // @internal
    protected checkFirstOption(preserveChecked?: boolean): void;
    // @internal
    protected checkLastOption(preserveChecked?: boolean): void;
    // @internal
    protected checkNextOption(preserveChecked?: boolean): void;
    // @internal
    protected checkPreviousOption(preserveChecked?: boolean): void;
    // @internal @override
    clickHandler(e: MouseEvent): boolean | void;
    // @internal @override (undocumented)
    connectedCallback(): void;
    // @internal @override (undocumented)
    disconnectedCallback(): void;
    // @internal
    get firstSelectedOptionIndex(): number;
    // @internal @override (undocumented)
    protected focusAndScrollOptionIntoView(): void;
    // @internal @override
    focusinHandler(e: FocusEvent): boolean | void;
    // @internal
    focusoutHandler(e: FocusEvent): void;
    // @internal @override
    keydownHandler(e: KeyboardEvent): boolean | void;
    // @internal @override
    mousedownHandler(e: MouseEvent): boolean | void;
    multiple: boolean;
    // @internal
    multipleChanged(prev: boolean | undefined, next: boolean): void;
    // @internal
    protected rangeStartIndex: number;
    // @override
    protected setSelectedOptions(): void;
    size: number;
    // @internal
    protected sizeChanged(prev: number | unknown, next: number): void;
    // @internal
    toggleSelectedForAllCheckedOptions(): void;
    // @internal @override (undocumented)
    typeaheadBufferChanged(prev: string, next: string): void;
    // @internal
    protected uncheckAllOptions(preserveChecked?: boolean): void;
}

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "ListboxOption" because one of its declarations is marked as @internal
//
// @public
export class ListboxOption extends FoundationElement {
    constructor(text?: string, value?: string, defaultSelected?: boolean, selected?: boolean);
    checked?: boolean;
    protected checkedChanged(prev: boolean | unknown, next?: boolean): void;
    content: Node[];
    // @internal
    protected contentChanged(prev: undefined | Node[], next: Node[]): void;
    defaultSelected: boolean;
    // (undocumented)
    protected defaultSelectedChanged(): void;
    dirtyValue: boolean;
    disabled: boolean;
    // (undocumented)
    protected disabledChanged(prev: boolean, next: boolean): void;
    // (undocumented)
    get form(): HTMLFormElement | null;
    protected initialValue: string;
    // (undocumented)
    initialValueChanged(previous: string, next: string): void;
    // (undocumented)
    get label(): string;
    // @internal (undocumented)
    proxy: HTMLOptionElement;
    selected: boolean;
    selectedAttribute: boolean;
    // (undocumented)
    protected selectedAttributeChanged(): void;
    // (undocumented)
    protected selectedChanged(): void;
    // (undocumented)
    get text(): string;
    set value(next: string);
    // (undocumented)
    get value(): string;
}

// @internal (undocumented)
export interface ListboxOption extends StartEnd, DelegatesARIAListboxOption {
}

// @public
export type ListboxOptionOptions = FoundationElementDefinition & StartEndOptions;

// @public
export const listboxOptionTemplate: FoundationElementTemplate<ViewTemplate<ListboxOption>, ListboxOptionOptions>;

// @public
export const listboxTemplate: FoundationElementTemplate<ViewTemplate<ListboxElement>>;

// @public
export abstract class MatchMediaBehavior implements Behavior {
    constructor(query: MediaQueryList);
    bind(source: typeof FASTElement & HTMLElement): void;
    protected abstract constructListener(source: typeof FASTElement): MediaQueryListListener;
    readonly query: MediaQueryList;
    unbind(source: typeof FASTElement & HTMLElement): void;
}

// @public
export class MatchMediaStyleSheetBehavior extends MatchMediaBehavior {
    constructor(query: MediaQueryList, styles: ElementStyles);
    // @internal
    protected constructListener(source: typeof FASTElement): MediaQueryListListener;
    readonly query: MediaQueryList;
    readonly styles: ElementStyles;
    // @internal
    unbind(source: typeof FASTElement & HTMLElement): void;
    static with(query: MediaQueryList): (styles: ElementStyles) => MatchMediaStyleSheetBehavior;
}

// @public
export type MediaQueryListListener = (this: MediaQueryList, ev?: MediaQueryListEvent) => void;

// @public
export class Menu extends FoundationElement {
    collapseExpandedItem(): void;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal (undocumented)
    disconnectedCallback(): void;
    focus(): void;
    // @internal
    handleFocusOut: (e: FocusEvent) => void;
    // @internal (undocumented)
    handleMenuKeyDown(e: KeyboardEvent): void | boolean;
    // @internal (undocumented)
    readonly isNestedMenu: () => boolean;
    // @internal (undocumented)
    items: HTMLSlotElement;
}

// @beta
export type menuConfigs = "bottom" | "bottom-fill" | "tallest" | "tallest-fill" | "top" | "top-fill";

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "MenuItem" because one of its declarations is marked as @internal
//
// @public
export class MenuItem extends FoundationElement {
    checked: boolean;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal
    currentDirection: Direction;
    disabled: boolean;
    // @internal (undocumented)
    disconnectedCallback(): void;
    expanded: boolean;
    // @internal (undocumented)
    handleMenuItemClick: (e: MouseEvent) => boolean;
    // @internal (undocumented)
    handleMenuItemKeyDown: (e: KeyboardEvent) => boolean;
    // @internal (undocumented)
    handleMouseOut: (e: MouseEvent) => boolean;
    // @internal (undocumented)
    handleMouseOver: (e: MouseEvent) => boolean;
    // @internal (undocumented)
    hasSubmenu: boolean;
    role: MenuItemRole;
    // @internal (undocumented)
    startColumnCount: MenuItemColumnCount;
    // @internal (undocumented)
    submenu: Element | undefined;
    // @internal (undocumented)
    submenuLoaded: () => void;
    // @internal
    submenuRegion: AnchoredRegion;
}

// @internal
export interface MenuItem extends StartEnd {
}

// @public
export type MenuItemColumnCount = 0 | 1 | 2;

// @public
export type MenuItemOptions = FoundationElementDefinition & StartEndOptions & {
    checkboxIndicator?: string | SyntheticViewTemplate;
    expandCollapseGlyph?: string | SyntheticViewTemplate;
    radioIndicator?: string | SyntheticViewTemplate;
};

// @public
export const MenuItemRole: {
    readonly menuitem: "menuitem";
    readonly menuitemcheckbox: "menuitemcheckbox";
    readonly menuitemradio: "menuitemradio";
};

// @public
export type MenuItemRole = typeof MenuItemRole[keyof typeof MenuItemRole];

// @public
export const menuItemTemplate: FoundationElementTemplate<ViewTemplate<MenuItem>, MenuItemOptions>;

// @public
export const menuTemplate: FoundationElementTemplate<ViewTemplate<Menu>>;

// @public
export type MonthFormat = "2-digit" | "long" | "narrow" | "numeric" | "short";

// @public
export type MonthInfo = {
    month: number;
    year: number;
    length: number;
    start: number;
};

// @public
export const newInstanceForScope: (key: any) => any;

// @public
export const newInstanceOf: (key: any) => any;

// Warning: (ae-internal-missing-underscore) The name "noninteractiveCalendarTemplate" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal
export const noninteractiveCalendarTemplate: (todayString: string) => ViewTemplate;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-forgotten-export) The symbol "FormAssociatedNumberField" needs to be exported by the entry point index.d.ts
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "NumberField" because one of its declarations is marked as @internal
//
// @public
export class NumberField extends FormAssociatedNumberField {
    autofocus: boolean;
    // @internal
    connectedCallback(): void;
    // @internal
    control: HTMLInputElement;
    // @internal
    defaultSlottedNodes: Node[];
    // @internal
    handleBlur(): void;
    // @internal
    handleChange(): void;
    // @internal
    handleKeyDown(e: KeyboardEvent): boolean;
    // @internal
    handleTextInput(): void;
    hideStep: boolean;
    list: string;
    max: number;
    // @internal
    maxChanged(previous: number, next: number): void;
    maxlength: number;
    min: number;
    // @internal
    minChanged(previous: number, next: number): void;
    minlength: number;
    placeholder: string;
    readOnly: boolean;
    protected select(): void;
    size: number;
    step: number;
    stepDown(): void;
    stepUp(): void;
    validate(): void;
    get valueAsNumber(): number;
    set valueAsNumber(next: number);
    // @internal
    valueChanged(previous: string, next: string): void;
}

// @internal
export interface NumberField extends StartEnd, DelegatesARIATextbox {
}

// @public
export type NumberFieldOptions = FoundationElementDefinition & StartEndOptions & {
    stepDownGlyph?: string | SyntheticViewTemplate;
    stepUpGlyph?: string | SyntheticViewTemplate;
};

// @public
export const numberFieldTemplate: FoundationElementTemplate<ViewTemplate<NumberField>, NumberFieldOptions>;

// @public
export const optional: (key: any) => any;

// @public
export type OverrideFoundationElementDefinition<T extends FoundationElementDefinition> = Partial<Omit<T, "type" | "baseClass">> & {
    prefix?: string;
};

// @public
export type ParentLocator = (owner: any) => Container | null;

// Warning: (ae-forgotten-export) The symbol "FormAssociatedPicker" needs to be exported by the entry point index.d.ts
//
// @alpha
export class Picker extends FormAssociatedPicker {
    // @internal
    activeListItemTemplate?: ViewTemplate;
    // @internal
    activeMenuOptionTemplate?: ViewTemplate;
    // @internal (undocumented)
    connectedCallback(): void;
    defaultListItemTemplate?: ViewTemplate;
    defaultMenuOptionTemplate?: ViewTemplate;
    // (undocumented)
    disconnectedCallback(): void;
    // @internal
    filteredOptionsList: string[];
    filterQuery: boolean;
    filterSelected: boolean;
    // @internal
    flyoutOpen: boolean;
    // @public
    focus(): void;
    handleFocusIn(e: FocusEvent): boolean;
    handleFocusOut(e: FocusEvent): boolean;
    handleItemInvoke(e: Event): boolean;
    handleKeyDown(e: KeyboardEvent): boolean;
    handleOptionInvoke(e: Event): boolean;
    handleRegionLoaded(e: Event): void;
    handleSelectionChange(): void;
    // @internal
    inputElement: HTMLInputElement;
    itemsPlaceholderElement: Node;
    label: string;
    labelledBy: string;
    // @internal
    listElement: PickerList;
    listItemContentsTemplate: ViewTemplate;
    listItemTemplate: ViewTemplate;
    loadingText: string;
    maxSelected: number | undefined;
    // @internal
    menuConfig: AnchoredRegionConfig;
    // @internal
    menuElement: PickerMenu;
    // @internal
    menuFocusIndex: number;
    // @internal
    menuFocusOptionId: string | undefined;
    // @internal
    menuId: string;
    menuOptionContentsTemplate: ViewTemplate;
    menuOptionTemplate: ViewTemplate;
    menuPlacement: menuConfigs;
    // @internal
    menuTag: string;
    noSuggestionsText: string;
    options: string;
    optionsList: string[];
    placeholder: string;
    query: string;
    // @internal
    region: AnchoredRegion;
    // @internal (undocumented)
    selectedItems: string[];
    // @internal
    selectedListTag: string;
    selection: string;
    showLoading: boolean;
    // @internal
    showNoOptions: boolean;
    suggestionsAvailableText: string;
}

// @alpha
export class PickerList extends FoundationElement {
}

// @alpha
export class PickerListItem extends FoundationElement {
    // @internal (undocumented)
    connectedCallback(): void;
    contentsTemplate: ViewTemplate;
    // @internal (undocumented)
    disconnectedCallback(): void;
    // (undocumented)
    handleClick(e: MouseEvent): boolean;
    // (undocumented)
    handleKeyDown(e: KeyboardEvent): boolean;
    value: string;
}

// Warning: (ae-incompatible-release-tags) The symbol "pickerListItemTemplate" is marked as @public, but its signature references "PickerListItem" which is marked as @alpha
//
// @public (undocumented)
export const pickerListItemTemplate: FoundationElementTemplate<ViewTemplate<PickerListItem>>;

// Warning: (ae-incompatible-release-tags) The symbol "pickerListTemplate" is marked as @public, but its signature references "PickerList" which is marked as @alpha
//
// @public (undocumented)
export const pickerListTemplate: FoundationElementTemplate<ViewTemplate<PickerList>>;

// @alpha
export class PickerMenu extends FoundationElement {
    // @internal
    footerElements: HTMLElement[];
    // (undocumented)
    footerElementsChanged(): void;
    // @internal
    headerElements: HTMLElement[];
    // (undocumented)
    headerElementsChanged(): void;
    // @internal
    menuElements: HTMLElement[];
    // (undocumented)
    menuElementsChanged(): void;
    // @internal
    optionElements: HTMLElement[];
    suggestionsAvailableText: string;
}

// @alpha
export class PickerMenuOption extends FoundationElement {
    // @internal (undocumented)
    connectedCallback(): void;
    contentsTemplate: ViewTemplate;
    // @internal (undocumented)
    disconnectedCallback(): void;
    // (undocumented)
    handleClick(e: MouseEvent): boolean;
    value: string;
}

// Warning: (ae-incompatible-release-tags) The symbol "pickerMenuOptionTemplate" is marked as @public, but its signature references "PickerMenuOption" which is marked as @alpha
//
// @public (undocumented)
export const pickerMenuOptionTemplate: FoundationElementTemplate<ViewTemplate<PickerMenuOption>>;

// Warning: (ae-incompatible-release-tags) The symbol "pickerMenuTemplate" is marked as @public, but its signature references "PickerMenu" which is marked as @alpha
//
// @public
export const pickerMenuTemplate: FoundationElementTemplate<ViewTemplate<PickerMenu>>;

// Warning: (ae-incompatible-release-tags) The symbol "pickerTemplate" is marked as @public, but its signature references "Picker" which is marked as @alpha
//
// @public
export const pickerTemplate: FoundationElementTemplate<ViewTemplate<Picker>>;

// @public
export type ProgressOptions = FoundationElementDefinition & {
    indeterminateIndicator1?: string | SyntheticViewTemplate;
    indeterminateIndicator2?: string | SyntheticViewTemplate;
};

// @public
export type ProgressRingOptions = FoundationElementDefinition & {
    indeterminateIndicator?: string | SyntheticViewTemplate;
};

// @public
export const progressRingTemplate: FoundationElementTemplate<ViewTemplate<BaseProgress>, ProgressRingOptions>;

// @public
export const progressTemplate: FoundationElementTemplate<ViewTemplate<BaseProgress>, ProgressOptions>;

// @public
export class PropertyStyleSheetBehavior implements Behavior {
    constructor(propertyName: string, value: any, styles: ElementStyles);
    bind(elementInstance: FASTElement): void;
    // @internal
    handleChange(source: FASTElement, key: string): void;
    // @internal
    unbind(source: typeof FASTElement & HTMLElement): void;
}

// @alpha
export type ProxyElement = HTMLSelectElement | HTMLTextAreaElement | HTMLInputElement;

// Warning: (ae-forgotten-export) The symbol "FormAssociatedRadio" needs to be exported by the entry point index.d.ts
//
// @public
export class Radio extends FormAssociatedRadio implements RadioControl {
    constructor();
    // @internal (undocumented)
    clickHandler(e: MouseEvent): boolean | void;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal (undocumented)
    defaultCheckedChanged(): void;
    // @internal (undocumented)
    defaultSlottedNodes: Node[];
    // @internal
    initialValue: string;
    // @internal (undocumented)
    keypressHandler: (e: KeyboardEvent) => boolean | void;
    name: string;
    readOnly: boolean;
}

// @public
export type RadioControl = Pick<HTMLInputElement, "checked" | "disabled" | "readOnly" | "focus" | "setAttribute" | "getAttribute">;

// @public
export class RadioGroup extends FoundationElement {
    // (undocumented)
    childItems: HTMLElement[];
    // @internal (undocumented)
    clickHandler: (e: MouseEvent) => void;
    // @internal (undocumented)
    connectedCallback(): void;
    disabled: boolean;
    // (undocumented)
    disconnectedCallback(): void;
    // @internal (undocumented)
    focusOutHandler: (e: FocusEvent) => boolean | void;
    // @internal
    keydownHandler: (e: KeyboardEvent) => boolean | void;
    name: string;
    // (undocumented)
    protected nameChanged(): void;
    orientation: Orientation | "horizontal" | "vertical";
    readOnly: boolean;
    // @internal (undocumented)
    slottedRadioButtons: HTMLElement[];
    value: string;
    // (undocumented)
    protected valueChanged(): void;
}

// @public
export const radioGroupTemplate: FoundationElementTemplate<ViewTemplate<RadioGroup>>;

// @public
export type RadioOptions = FoundationElementDefinition & {
    checkedIndicator?: string | SyntheticViewTemplate;
};

// @public
export const radioTemplate: FoundationElementTemplate<ViewTemplate<Radio>, RadioOptions>;

// @beta
export function reflectAttributes<T = any>(...attributes: string[]): CaptureType<T>;

// @public
export type RegisterSelf<T extends Constructable> = {
    register(container: Container): Resolver<InstanceType<T>>;
    registerInRequestor: boolean;
};

// @public
export interface Registration<K = any> {
    register(container: Container): Resolver<K>;
}

// @public
export const Registration: Readonly<{
    instance<T>(key: Key, value: T): Registration<T>;
    singleton<T_1 extends Constructable<{}>>(key: Key, value: T_1): Registration<InstanceType<T_1>>;
    transient<T_2 extends Constructable<{}>>(key: Key, value: T_2): Registration<InstanceType<T_2>>;
    callback<T_3>(key: Key, callback: ResolveCallback<T_3>): Registration<Resolved<T_3>>;
    cachedCallback<T_4>(key: Key, callback: ResolveCallback<T_4>): Registration<Resolved<T_4>>;
    aliasTo<T_5>(originalKey: T_5, aliasKey: Key): Registration<Resolved<T_5>>;
}>;

// @public
export interface Registry {
    register(container: Container, ...params: unknown[]): void | Resolver;
}

// @public
export type ResolveCallback<T = any> = (handler: Container, requestor: Container, resolver: Resolver<T>) => T;

// Warning: (ae-forgotten-export) The symbol "ResolverLike" needs to be exported by the entry point index.d.ts
//
// @public
export type Resolved<K> = K extends InterfaceSymbol<infer T> ? T : K extends Constructable ? InstanceType<K> : K extends ResolverLike<any, infer T1> ? T1 extends Constructable ? InstanceType<T1> : T1 : K;

// @public
export interface Resolver<K = any> extends ResolverLike<Container, K> {
}

// @public
export class ResolverBuilder<K> {
    constructor(container: Container, key: Key);
    aliasTo(destinationKey: Key): Resolver<K>;
    cachedCallback(value: ResolveCallback<K>): Resolver<K>;
    callback(value: ResolveCallback<K>): Resolver<K>;
    instance(value: K): Resolver<K>;
    singleton(value: Constructable): Resolver<K>;
    transient(value: Constructable): Resolver<K>;
}

// Warning: (ae-internal-missing-underscore) The name "ResolverImpl" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal (undocumented)
export class ResolverImpl implements Resolver, Registration {
    // (undocumented)
    get $isResolver(): true;
    constructor(key: Key, strategy: ResolverStrategy, state: any);
    // (undocumented)
    getFactory(container: Container): Factory | null;
    // (undocumented)
    key: Key;
    // (undocumented)
    register(container: Container): Resolver;
    // (undocumented)
    resolve(handler: Container, requestor: Container): any;
    // (undocumented)
    state: any;
    // (undocumented)
    strategy: ResolverStrategy;
}

// Warning: (ae-internal-missing-underscore) The name "ResolverStrategy" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal (undocumented)
export const enum ResolverStrategy {
    // (undocumented)
    alias = 5,
    // (undocumented)
    array = 4,
    // (undocumented)
    callback = 3,
    // (undocumented)
    instance = 0,
    // (undocumented)
    singleton = 1,
    // (undocumented)
    transient = 2
}

// Warning: (ae-internal-missing-underscore) The name "roleForMenuItem" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal (undocumented)
export const roleForMenuItem: {
    [value in keyof typeof MenuItemRole]: typeof MenuItemRole[value];
};

// @public
export type ScrollEasing = "linear" | "ease-in" | "ease-out" | "ease-in-out" | string;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-forgotten-export) The symbol "FormAssociatedSearch" needs to be exported by the entry point index.d.ts
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Search" because one of its declarations is marked as @internal
//
// @public
export class Search extends FormAssociatedSearch {
    autofocus: boolean;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal
    control: HTMLInputElement;
    // @internal (undocumented)
    defaultSlottedNodes: Node[];
    // @internal
    handleChange(): void;
    handleClearInput(): void;
    // @internal
    handleTextInput(): void;
    list: string;
    maxlength: number;
    minlength: number;
    pattern: string;
    placeholder: string;
    readOnly: boolean;
    // @internal
    root: HTMLDivElement;
    size: number;
    spellcheck: boolean;
    validate(): void;
}

// @internal
export interface Search extends StartEnd, DelegatesARIASearch {
}

// @public
export type SearchOptions = FoundationElementDefinition & StartEndOptions;

// @public
export const searchTemplate: FoundationElementTemplate<ViewTemplate<Search>, SearchOptions>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-forgotten-export) The symbol "FormAssociatedSelect" needs to be exported by the entry point index.d.ts
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Select" because one of its declarations is marked as @internal
//
// @public
export class Select extends FormAssociatedSelect {
    // @internal
    clickHandler(e: MouseEvent): boolean | void;
    // @internal
    get collapsible(): boolean;
    // (undocumented)
    connectedCallback(): void;
    // @internal
    control: HTMLElement;
    // @internal
    disabledChanged(prev: boolean, next: boolean): void;
    // (undocumented)
    disconnectedCallback(): void;
    get displayValue(): string;
    // @internal
    focusoutHandler(e: FocusEvent): boolean | void;
    // @internal
    formResetCallback(): void;
    // @internal @override
    handleChange(source: any, propertyName: string): void;
    // @internal
    keydownHandler(e: KeyboardEvent): boolean | void;
    // @internal
    listbox: HTMLDivElement;
    // @internal
    listboxId: string;
    // @internal
    maxHeight: number;
    // @internal @override
    mousedownHandler(e: MouseEvent): boolean | void;
    multipleChanged(prev: boolean | undefined, next: boolean): void;
    open: boolean;
    // @internal
    protected openChanged(prev: boolean | undefined, next: boolean): void;
    position?: SelectPosition;
    positionAttribute?: SelectPosition;
    // (undocumented)
    protected positionChanged(prev: SelectPosition | undefined, next: SelectPosition | undefined): void;
    // @internal
    selectedIndexChanged(prev: number | undefined, next: number): void;
    // @internal @override
    protected selectedOptionsChanged(prev: ListboxOption[] | undefined, next: ListboxOption[]): void;
    // @internal @override
    protected setDefaultSelectedOption(): void;
    setPositioning(): void;
    // @internal @override
    protected sizeChanged(prev: number | undefined, next: number): void;
    // @internal
    slottedOptionsChanged(prev: Element[] | undefined, next: Element[]): void;
    get value(): string;
    set value(next: string);
}

// @internal (undocumented)
export interface Select extends StartEnd, DelegatesARIASelect {
}

// @public
export type SelectOptions = FoundationElementDefinition & StartEndOptions & {
    indicator?: string | SyntheticViewTemplate;
};

// @public
export const SelectPosition: {
    readonly above: "above";
    readonly below: "below";
};

// @public
export type SelectPosition = typeof SelectPosition[keyof typeof SelectPosition];

// @public
export const selectTemplate: FoundationElementTemplate<ViewTemplate<Select>, SelectOptions>;

// @public
export interface ServiceLocator {
    get<K extends Key>(key: K): Resolved<K>;
    get<K extends Key>(key: Key): Resolved<K>;
    get<K extends Key>(key: K | Key): Resolved<K>;
    getAll<K extends Key>(key: K, searchAncestors?: boolean): readonly Resolved<K>[];
    getAll<K extends Key>(key: Key, searchAncestors?: boolean): readonly Resolved<K>[];
    getAll<K extends Key>(key: K | Key, searchAncestors?: boolean): readonly Resolved<K>[];
    has<K extends Key>(key: K | Key, searchAncestors: boolean): boolean;
}

// @public
export const ServiceLocator: InterfaceSymbol<ServiceLocator>;

// Warning: (ae-forgotten-export) The symbol "singletonDecorator" needs to be exported by the entry point index.d.ts
//
// @public
export function singleton<T extends Constructable>(): typeof singletonDecorator;

// @public (undocumented)
export function singleton<T extends Constructable>(options?: SingletonOptions): typeof singletonDecorator;

// @public
export function singleton<T extends Constructable>(target: T & Partial<RegisterSelf<T>>): T & RegisterSelf<T>;

// @public
export class Skeleton extends FoundationElement {
    fill: string;
    pattern: string;
    shape: SkeletonShape;
    shimmer: boolean;
}

// @public
export type SkeletonShape = "rect" | "circle";

// @public
export const skeletonTemplate: FoundationElementTemplate<ViewTemplate<Skeleton>>;

// Warning: (ae-forgotten-export) The symbol "FormAssociatedSlider" needs to be exported by the entry point index.d.ts
//
// @public
export class Slider extends FormAssociatedSlider implements SliderConfiguration {
    // @internal (undocumented)
    connectedCallback(): void;
    decrement(): void;
    // @internal (undocumented)
    direction: Direction;
    // @internal (undocumented)
    disconnectedCallback(): void;
    increment(): void;
    // @internal (undocumented)
    initialValue: string;
    // @internal (undocumented)
    isDragging: boolean;
    // (undocumented)
    protected keypressHandler: (e: KeyboardEvent) => void;
    max: number;
    min: number;
    mode: SliderMode;
    orientation: Orientation;
    // @internal (undocumented)
    position: string;
    readOnly: boolean;
    step: number;
    // @internal (undocumented)
    stepMultiplier: number;
    // @internal (undocumented)
    thumb: HTMLDivElement;
    // @internal (undocumented)
    track: HTMLDivElement;
    // @internal (undocumented)
    trackHeight: number;
    // @internal (undocumented)
    trackLeft: number;
    // @internal (undocumented)
    trackMinHeight: number;
    // @internal (undocumented)
    trackMinWidth: number;
    // @internal (undocumented)
    trackWidth: number;
    get valueAsNumber(): number;
    set valueAsNumber(next: number);
    // @internal (undocumented)
    valueChanged(previous: string, next: string): void;
    valueTextFormatter: (value: string) => string | null;
}

// @public
export interface SliderConfiguration {
    // (undocumented)
    direction?: Direction;
    // (undocumented)
    disabled?: boolean;
    // (undocumented)
    max: number;
    // (undocumented)
    min: number;
    // (undocumented)
    orientation?: Orientation;
}

// @public
export class SliderLabel extends FoundationElement {
    // @internal (undocumented)
    connectedCallback(): void;
    disabled: boolean;
    // @internal (undocumented)
    disconnectedCallback(): void;
    // @internal (undocumented)
    handleChange(source: any, propertyName: string): void;
    hideMark: boolean;
    position: string;
    // @internal (undocumented)
    positionStyle: string;
    // @internal (undocumented)
    root: HTMLDivElement;
    // @internal (undocumented)
    sliderDirection: Direction;
    // @internal (undocumented)
    sliderMaxPosition: number;
    // @internal (undocumented)
    sliderMinPosition: number;
    // @internal (undocumented)
    sliderOrientation: Orientation;
    // @internal (undocumented)
    protected sliderOrientationChanged(): void;
}

// @public
export const sliderLabelTemplate: FoundationElementTemplate<ViewTemplate<SliderLabel>>;

// @public
export const SliderMode: {
    readonly singleValue: "single-value";
};

// @public
export type SliderMode = typeof SliderMode[keyof typeof SliderMode];

// @public
export type SliderOptions = FoundationElementDefinition & {
    thumb?: string | SyntheticViewTemplate;
};

// @public
export const sliderTemplate: FoundationElementTemplate<ViewTemplate<Slider>, SliderOptions>;

// @public
export class StartEnd {
    // (undocumented)
    end: HTMLSlotElement;
    // (undocumented)
    endContainer: HTMLSpanElement;
    // (undocumented)
    handleEndContentChange(): void;
    // (undocumented)
    handleStartContentChange(): void;
    // (undocumented)
    start: HTMLSlotElement;
    // (undocumented)
    startContainer: HTMLSpanElement;
}

// @public
export type StartEndOptions = StartOptions & EndOptions;

// @public
export type StartOptions = {
    start?: string | SyntheticViewTemplate;
};

// @public
export const startSlotTemplate: (context: ElementDefinitionContext, definition: StartOptions) => ViewTemplate<StartEnd>;

// @public @deprecated
export const startTemplate: ViewTemplate<StartEnd>;

// @public
export type StaticDesignTokenValue<T> = T extends Function ? never : T;

// @alpha (undocumented)
export const supportsElementInternals: boolean;

// Warning: (ae-forgotten-export) The symbol "FormAssociatedSwitch" needs to be exported by the entry point index.d.ts
//
// @public
export class Switch extends FormAssociatedSwitch {
    constructor();
    // @internal (undocumented)
    checkedChanged(prev: boolean | undefined, next: boolean): void;
    // @internal (undocumented)
    clickHandler: (e: MouseEvent) => void;
    // @internal (undocumented)
    defaultSlottedNodes: Node[];
    // @internal
    initialValue: string;
    // @internal (undocumented)
    keypressHandler: (e: KeyboardEvent) => void;
    readOnly: boolean;
}

// @public
export type SwitchOptions = FoundationElementDefinition & {
    switch?: string | SyntheticViewTemplate;
};

// @public
export const switchTemplate: FoundationElementTemplate<ViewTemplate<Switch>, SwitchOptions>;

// @public
export class Tab extends FoundationElement {
    disabled: boolean;
}

// @public
export class TabPanel extends FoundationElement {
}

// @public
export const tabPanelTemplate: FoundationElementTemplate<ViewTemplate<TabPanel>>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Tabs" because one of its declarations is marked as @internal
//
// @public
export class Tabs extends FoundationElement {
    activeid: string;
    // @internal (undocumented)
    activeidChanged(oldValue: string, newValue: string): void;
    activeindicator: boolean;
    // @internal (undocumented)
    activeIndicatorRef: HTMLElement;
    activetab: HTMLElement;
    adjust(adjustment: number): void;
    // @internal (undocumented)
    connectedCallback(): void;
    orientation: TabsOrientation;
    // @internal (undocumented)
    orientationChanged(): void;
    // @internal (undocumented)
    showActiveIndicator: boolean;
    // @internal (undocumented)
    tabpanels: HTMLElement[];
    // @internal (undocumented)
    tabpanelsChanged(): void;
    // @internal (undocumented)
    tabs: HTMLElement[];
    // @internal (undocumented)
    tabsChanged(): void;
}

// @internal
export interface Tabs extends StartEnd {
}

// @public
export type TabsOptions = FoundationElementDefinition & StartEndOptions;

// @public
export const TabsOrientation: {
    readonly vertical: "vertical";
    readonly horizontal: "horizontal";
};

// @public
export type TabsOrientation = typeof TabsOrientation[keyof typeof TabsOrientation];

// @public
export const tabsTemplate: FoundationElementTemplate<ViewTemplate<Tabs>, TabsOptions>;

// @public
export const tabTemplate: FoundationElementTemplate<ViewTemplate<Tab>>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-forgotten-export) The symbol "FormAssociatedTextArea" needs to be exported by the entry point index.d.ts
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "TextArea" because one of its declarations is marked as @internal
//
// @public
export class TextArea extends FormAssociatedTextArea {
    autofocus: boolean;
    cols: number;
    // @internal
    control: HTMLTextAreaElement;
    // @internal (undocumented)
    defaultSlottedNodes: Node[];
    formId: string;
    // @internal
    handleChange(): void;
    // @internal (undocumented)
    handleTextInput: () => void;
    list: string;
    maxlength: number;
    minlength: number;
    name: string;
    placeholder: string;
    readOnly: boolean;
    resize: TextAreaResize;
    rows: number;
    protected select(): void;
    spellcheck: boolean;
    validate(): void;
}

// @internal
export interface TextArea extends DelegatesARIATextbox {
}

// @public
export const TextAreaResize: {
    readonly none: "none";
    readonly both: "both";
    readonly horizontal: "horizontal";
    readonly vertical: "vertical";
};

// @public
export type TextAreaResize = typeof TextAreaResize[keyof typeof TextAreaResize];

// @public
export const textAreaTemplate: FoundationElementTemplate<ViewTemplate<TextArea>>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-forgotten-export) The symbol "FormAssociatedTextField" needs to be exported by the entry point index.d.ts
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "TextField" because one of its declarations is marked as @internal
//
// @public
export class TextField extends FormAssociatedTextField {
    autofocus: boolean;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal
    control: HTMLInputElement;
    // @internal (undocumented)
    defaultSlottedNodes: Node[];
    // @internal
    handleChange(): void;
    // @internal
    handleTextInput(): void;
    list: string;
    maxlength: number;
    minlength: number;
    pattern: string;
    placeholder: string;
    readOnly: boolean;
    protected select(): void;
    size: number;
    spellcheck: boolean;
    type: TextFieldType;
    validate(): void;
}

// @internal
export interface TextField extends StartEnd, DelegatesARIATextbox {
}

// @public
export type TextFieldOptions = FoundationElementDefinition & StartEndOptions;

// @public
export const textFieldTemplate: FoundationElementTemplate<ViewTemplate<TextField>, TextFieldOptions>;

// @public
export const TextFieldType: {
    readonly email: "email";
    readonly password: "password";
    readonly tel: "tel";
    readonly text: "text";
    readonly url: "url";
};

// @public
export type TextFieldType = typeof TextFieldType[keyof typeof TextFieldType];

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "Toolbar" because one of its declarations is marked as @internal
//
// @public
export class Toolbar extends FoundationElement {
    // @internal
    get activeIndex(): number;
    set activeIndex(value: number);
    // @internal
    protected get allSlottedItems(): (HTMLElement | Node)[];
    // (undocumented)
    childItems: Element[];
    // (undocumented)
    protected childItemsChanged(prev: undefined | Element[], next: Element[]): void;
    // @internal (undocumented)
    connectedCallback(): void;
    // @internal
    direction: Direction;
    // @internal
    focusinHandler(e: FocusEvent): boolean | void;
    // @internal
    keydownHandler(e: KeyboardEvent): boolean | void;
    // @internal
    mouseDownHandler(e: MouseEvent): boolean | void;
    orientation: Orientation;
    // @internal
    protected reduceFocusableElements(): void;
    // @internal
    slottedItems: HTMLElement[];
    // (undocumented)
    protected slottedItemsChanged(): void;
    // @internal
    slottedLabel: HTMLElement[];
}

// @internal (undocumented)
export interface Toolbar extends StartEnd, DelegatesARIAToolbar {
}

// @public
export type ToolbarOptions = FoundationElementDefinition & StartEndOptions;

// @public
export const toolbarTemplate: FoundationElementTemplate<ViewTemplate<Toolbar>, ToolbarOptions>;

// @public
export class Tooltip extends FoundationElement {
    anchor: string;
    anchorElement: HTMLElement | null;
    autoUpdateMode: AutoUpdateMode;
    // (undocumented)
    connectedCallback(): void;
    // @internal
    currentDirection: Direction;
    delay: number;
    // (undocumented)
    disconnectedCallback(): void;
    // @internal
    handlePositionChange: (ev: Event) => void;
    // @internal (undocumented)
    horizontalDefaultPosition: string | undefined;
    // @internal (undocumented)
    horizontalInset: string;
    // @internal (undocumented)
    horizontalPositioningMode: AxisPositioningMode;
    // @internal (undocumented)
    horizontalScaling: AxisScalingMode;
    horizontalViewportLock: boolean;
    position: TooltipPosition;
    // @internal
    region: AnchoredRegion;
    // @internal (undocumented)
    tooltipVisible: boolean;
    // @internal (undocumented)
    verticalDefaultPosition: string | undefined;
    // @internal (undocumented)
    verticalInset: string;
    // @internal (undocumented)
    verticalPositioningMode: AxisPositioningMode;
    // @internal (undocumented)
    verticalScaling: AxisScalingMode;
    verticalViewportLock: boolean;
    // @internal
    viewportElement: HTMLElement | null;
    visible: boolean;
}

// @public
export const TooltipPosition: {
    readonly top: "top";
    readonly right: "right";
    readonly bottom: "bottom";
    readonly left: "left";
    readonly start: "start";
    readonly end: "end";
    readonly topLeft: "top-left";
    readonly topRight: "top-right";
    readonly bottomLeft: "bottom-left";
    readonly bottomRight: "bottom-right";
    readonly topStart: "top-start";
    readonly topEnd: "top-end";
    readonly bottomStart: "bottom-start";
    readonly bottomEnd: "bottom-end";
};

// @public
export type TooltipPosition = typeof TooltipPosition[keyof typeof TooltipPosition];

// @public
export const tooltipTemplate: FoundationElementTemplate<ViewTemplate<Tooltip>>;

// @public
type Transformer_2<K> = (instance: Resolved<K>) => Resolved<K>;
export { Transformer_2 as Transformer }

// Warning: (ae-forgotten-export) The symbol "transientDecorator" needs to be exported by the entry point index.d.ts
//
// @public
export function transient<T extends Constructable>(): typeof transientDecorator;

// @public
export function transient<T extends Constructable>(target: T & Partial<RegisterSelf<T>>): T & RegisterSelf<T>;

// Warning: (ae-different-release-tags) This symbol has another declaration with a different release tag
// Warning: (ae-internal-mixed-release-tag) Mixed release tags are not allowed for "TreeItem" because one of its declarations is marked as @internal
//
// @public
export class TreeItem extends FoundationElement {
    // @internal
    childItemLength(): number;
    // @internal (undocumented)
    childItems: HTMLElement[];
    disabled: boolean;
    // @internal
    expandCollapseButton: HTMLDivElement;
    expanded: boolean;
    // @internal
    focusable: boolean;
    static focusItem(el: HTMLElement): void;
    // @internal
    handleBlur: (e: FocusEvent) => void;
    // @internal
    handleExpandCollapseButtonClick: (e: MouseEvent) => void;
    // @internal
    handleFocus: (e: FocusEvent) => void;
    readonly isNestedItem: () => boolean;
    // @internal
    items: HTMLElement[];
    // @internal
    nested: boolean;
    // @internal (undocumented)
    renderCollapsedChildren: boolean;
    selected: boolean;
}

// @internal
export interface TreeItem extends StartEnd {
}

// @public
export type TreeItemOptions = FoundationElementDefinition & StartEndOptions & {
    expandCollapseGlyph?: string | SyntheticViewTemplate;
};

// @public
export const treeItemTemplate: FoundationElementTemplate<ViewTemplate<TreeItem>, TreeItemOptions>;

// @public
export class TreeView extends FoundationElement {
    // (undocumented)
    connectedCallback(): void;
    // @internal
    currentFocused: HTMLElement | TreeItem | null;
    currentSelected: HTMLElement | TreeItem | null;
    // @internal
    handleBlur: (e: FocusEvent) => void;
    // @internal
    handleClick(e: Event): boolean | void;
    // @internal
    handleFocus: (e: FocusEvent) => void;
    // @internal
    handleKeyDown: (e: KeyboardEvent) => boolean | void;
    // @internal
    handleSelectedChange: (e: Event) => boolean | void;
    renderCollapsedNodes: boolean;
    // @internal
    slottedTreeItems: HTMLElement[];
    // @internal
    treeView: HTMLElement;
}

// @public
export const treeViewTemplate: FoundationElementTemplate<ViewTemplate<TreeView>>;

// Warning: (ae-internal-missing-underscore) The name "validateKey" should be prefixed with an underscore because the declaration is marked as @internal
//
// @internal (undocumented)
export function validateKey(key: any): void;

// @public
export type VerticalPosition = "top" | "bottom" | "center" | "unset";

// @public
export type WeekdayFormat = "long" | "narrow" | "short";

// @public
export function whitespaceFilter(value: Node, index: number, array: Node[]): boolean;

// @public
export type YearFormat = "2-digit" | "numeric";

// Warnings were encountered during analysis:
//
// dist/dts/design-token/design-token.d.ts:95:5 - (ae-forgotten-export) The symbol "create" needs to be exported by the entry point index.d.ts
// dist/dts/di/di.d.ts:513:5 - (ae-forgotten-export) The symbol "SingletonOptions" needs to be exported by the entry point index.d.ts

// (No @packageDocumentation comment for this package)

```
