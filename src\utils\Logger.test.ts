import { Logger } from './Logger';

describe('Logger', () => {
  beforeEach(() => {
    // Reset console spies before each test
    (global as any).consoleSpy.log.mockClear();
    (global as any).consoleSpy.error.mockClear();
    (global as any).consoleSpy.warn.mockClear();
    (global as any).consoleSpy.info.mockClear();
  });

  describe('info', () => {
    it('should log info messages', () => {
      const message = 'Test info message';
      Logger.info(message);

      expect((global as any).consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[INFO]'),
        expect.stringContaining(message)
      );
    });

    it('should include timestamp in log message', () => {
      Logger.info('Test message');

      expect(global.consoleSpy.log).toHaveBeenCalledWith(
        expect.stringMatching(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\]/),
        expect.any(String)
      );
    });
  });

  describe('error', () => {
    it('should log error messages', () => {
      const message = 'Test error message';
      Logger.error(message);

      expect(global.consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR]'),
        expect.stringContaining(message)
      );
    });

    it('should log error objects', () => {
      const error = new Error('Test error');
      Logger.error('Error occurred', error);

      expect(global.consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR]'),
        expect.stringContaining('Error occurred'),
        error
      );
    });
  });

  describe('warn', () => {
    it('should log warning messages', () => {
      const message = 'Test warning message';
      Logger.warn(message);

      expect(global.consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining('[WARN]'),
        expect.stringContaining(message)
      );
    });
  });

  describe('debug', () => {
    it('should log debug messages', () => {
      const message = 'Test debug message';
      Logger.debug(message);

      expect(global.consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG]'),
        expect.stringContaining(message)
      );
    });
  });
});
