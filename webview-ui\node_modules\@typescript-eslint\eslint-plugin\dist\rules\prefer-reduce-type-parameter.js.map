{"version": 3, "file": "prefer-reduce-type-parameter.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-reduce-type-parameter.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAKiB;AAMjB,MAAM,uBAAuB,GAAG,CAC9B,MAAiC,EAClB,EAAE;IACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrB,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,IACE,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;QAC/C,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,EACzC,CAAC;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;IAC/B,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,6EAA6E;YAC/E,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,mBAAmB,EACjB,gFAAgF;SACnF;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,OAAO;YACL,0CAA0C,CACxC,MAAgD;gBAEhD,IAAI,uBAAuB,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACjD,OAAO;gBACT,CAAC;gBAED,MAAM,CAAC,EAAE,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;gBAE9C,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAA,sBAAe,EAAC,SAAS,CAAC,EAAE,CAAC;oBACtE,OAAO;gBACT,CAAC;gBAED,yCAAyC;gBACzC,MAAM,aAAa,GAAG,IAAA,mCAA4B,EAChD,QAAQ,EACR,MAAM,CAAC,MAAM,CACd,CAAC;gBAEF,+CAA+C;gBAC/C,IAAI,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;oBACvC,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,qBAAqB;wBAChC,IAAI,EAAE,SAAS;wBACf,GAAG,EAAE,KAAK,CAAC,EAAE;4BACX,MAAM,KAAK,GAAG;gCACZ,KAAK,CAAC,WAAW,CAAC;oCAChB,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;oCAClB,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;iCAC9B,CAAC;gCACF,KAAK,CAAC,WAAW,CAAC;oCAChB,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oCAC7B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;iCACnB,CAAC;6BACH,CAAC;4BAEF,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gCACjC,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,eAAe,CACnB,MAAM,EACN,IAAI,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC,OAAO,CAChC,SAAS,CAAC,cAAc,CACzB,GAAG,CACL,CACF,CAAC;4BACJ,CAAC;4BAED,OAAO,KAAK,CAAC;wBACf,CAAC;qBACF,CAAC,CAAC;oBAEH,OAAO;gBACT,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}