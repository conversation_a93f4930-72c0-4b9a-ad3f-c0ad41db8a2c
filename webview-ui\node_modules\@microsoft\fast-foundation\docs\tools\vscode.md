---
id: vscode
title: Visual Studio Code
sidebar_label: Visual Studio Code
custom_edit_url: https://github.com/microsoft/fast/edit/master/packages/web-components/fast-foundation/docs/tools/vscode.md
description: You can use any code editor you like when working with FAST. One of our favorites is Visual Studio Code.
---

You can use any code editor you like when working with FAST. One of our favorites is [Visual Studio Code](https://code.visualstudio.com/). VS Code has great support for API autocomplete for TypeScript and JavaScript APIs, as well as a rich ecosystem of plugins.

When working with VS Code, we recommend using these plugins:

* [FAST Snippets](https://marketplace.visualstudio.com/items?itemName=kingoftac.fast-snippets) to get commonly used conventions when creating `FAST Components`.
* [literally-html](https://marketplace.visualstudio.com/items?itemName=webreflection.literally-html) to get syntax highlighting and documentation in your `html` blocks.
* [es6-string-css](https://marketplace.visualstudio.com/items?itemName=bashmish.es6-string-css) to get syntax highlighting in your `css` blocks.