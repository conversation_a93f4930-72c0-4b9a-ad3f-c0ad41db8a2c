/**
 * Jest setup file for VS Code extension testing
 * This file is executed before each test file
 */

// Mock VS Code API for unit tests
const mockVSCode = {
  commands: {
    registerCommand: jest.fn(),
    executeCommand: jest.fn(),
  },
  window: {
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    createTreeView: jest.fn(),
    createWebviewPanel: jest.fn(),
    registerTreeDataProvider: jest.fn(),
  },
  workspace: {
    getConfiguration: jest.fn(() => ({
      get: jest.fn(),
      update: jest.fn(),
    })),
    workspaceFolders: [],
    onDidChangeWorkspaceFolders: jest.fn(),
    createFileSystemWatcher: jest.fn(() => ({
      onDidCreate: jest.fn(),
      onDidChange: jest.fn(),
      onDidDelete: jest.fn(),
      dispose: jest.fn(),
    })),
    findFiles: jest.fn(),
  },
  Uri: {
    file: jest.fn((path: string) => ({ fsPath: path, scheme: 'file', path })),
    parse: jest.fn(),
  },
  Range: jest.fn(),
  Position: jest.fn(),
  ExtensionContext: jest.fn(),
  Disposable: jest.fn(() => ({
    dispose: jest.fn(),
  })),
  EventEmitter: jest.fn(() => ({
    event: jest.fn(),
    fire: jest.fn(),
    dispose: jest.fn(),
  })),
  TreeItemCollapsibleState: {
    None: 0,
    Collapsed: 1,
    Expanded: 2,
  },
  ViewColumn: {
    One: 1,
    Two: 2,
    Three: 3,
  },
  env: {
    openExternal: jest.fn(),
  },
};

// Mock the vscode module
jest.mock('vscode', () => mockVSCode, { virtual: true });

// Global test utilities
(global as any).mockVSCode = mockVSCode;

// Setup console spy to track console outputs in tests
(global as any).consoleSpy = {
  log: jest.spyOn(console, 'log').mockImplementation(),
  error: jest.spyOn(console, 'error').mockImplementation(),
  warn: jest.spyOn(console, 'warn').mockImplementation(),
  info: jest.spyOn(console, 'info').mockImplementation(),
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
  (global as any).consoleSpy.log.mockClear();
  (global as any).consoleSpy.error.mockClear();
  (global as any).consoleSpy.warn.mockClear();
  (global as any).consoleSpy.info.mockClear();
});

// Global test timeout
jest.setTimeout(10000);
