{"version": 3, "file": "method-signature-style.js", "sourceRoot": "", "sources": ["../../src/rules/method-signature-style.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAOiB;AAKjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;SAClE;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,WAAW,EACT,2EAA2E;YAC7E,aAAa,EACX,2EAA2E;SAC9E;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;aAC7B;SACF;KACF;IACD,cAAc,EAAE,CAAC,UAAU,CAAC;IAE5B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C,SAAS,YAAY,CACnB,IAA+D;YAE/D,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;YACnB,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;YAClB,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,GAAG,GAAG,YAAY,GAAG,EAAE,CAAC;YAC1B,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,SAAS,eAAe,CACtB,IAA0D;YAE1D,IAAI,MAAM,GAAG,IAAI,CAAC;YAClB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,YAAY,GAAG,IAAA,iBAAU,EAC7B,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,0BAAmB,CAAC,EAC9D,8CAA8C,CAC/C,CAAC;gBACF,MAAM,YAAY,GAAG,IAAA,iBAAU,EAC7B,UAAU,CAAC,aAAa,CACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EACnC,0BAAmB,CACpB,EACD,4CAA4C,CAC7C,CAAC;gBAEF,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAChC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EACrB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CACtB,CAAC;YACJ,CAAC;YACD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;gBAChC,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC3D,MAAM,GAAG,GAAG,UAAU,GAAG,MAAM,EAAE,CAAC;YACpC,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,SAAS,mBAAmB,CAC1B,IAA0D;YAE1D,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI;gBAC5B,CAAC,CAAC,2EAA2E;oBAC3E,qDAAqD;oBACrD,KAAK;gBACP,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACzD,CAAC;QAED,SAAS,YAAY,CAAC,IAAmB;YACvC,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAChD,IACE,SAAS;gBACT,CAAC,IAAA,uBAAgB,EAAC,SAAS,CAAC,IAAI,IAAA,mBAAY,EAAC,SAAS,CAAC,CAAC,EACxD,CAAC;gBACD,OAAO,SAAS,CAAC,KAAK,CAAC;YACzB,CAAC;YAED,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,SAAS,6BAA6B,CAAC,IAAmB;YACxD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;gBAC5D,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;gBAChD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC;QAED,OAAO;YACL,GAAG,CAAC,IAAI,KAAK,UAAU,IAAI;gBACzB,iBAAiB,CAAC,UAAU;oBAC1B,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACjC,OAAO;oBACT,CAAC;oBAED,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;oBACjC,MAAM,OAAO,GACX,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBAC5C,CAAC,CAAC,MAAM,CAAC,IAAI;wBACb,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;4BAC5C,CAAC,CAAC,MAAM,CAAC,OAAO;4BAChB,CAAC,CAAC,EAAE,CAAC;oBAEX,MAAM,wBAAwB,GAC5B,OAAO,CAAC,MAAM,CACZ,CAAC,OAAO,EAAyC,EAAE,CACjD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;wBACjD,OAAO,KAAK,UAAU;wBACtB,YAAY,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,UAAU,CAAC,CACrD,CAAC;oBACJ,MAAM,cAAc,GAAG,6BAA6B,CAAC,UAAU,CAAC,CAAC;oBAEjE,IAAI,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACxC,IAAI,cAAc,EAAE,CAAC;4BACnB,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,UAAU;gCAChB,SAAS,EAAE,aAAa;6BACzB,CAAC,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACN,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,UAAU;gCAChB,SAAS,EAAE,aAAa;gCACxB,CAAC,GAAG,CAAC,KAAK;oCACR,MAAM,WAAW,GAAG;wCAClB,UAAU;wCACV,GAAG,wBAAwB;qCAC5B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCACrD,MAAM,UAAU,GAAG,WAAW;yCAC3B,GAAG,CAAC,IAAI,CAAC,EAAE;wCACV,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;wCACrC,MAAM,UAAU,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;wCAC7C,OAAO,IAAI,MAAM,OAAO,UAAU,GAAG,CAAC;oCACxC,CAAC,CAAC;yCACD,IAAI,CAAC,KAAK,CAAC,CAAC;oCACf,MAAM,GAAG,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;oCACrC,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;oCAC3C,MAAM,KAAK,CAAC,WAAW,CACrB,UAAU,EACV,GAAG,GAAG,KAAK,UAAU,GAAG,SAAS,EAAE,CACpC,CAAC;oCACF,KAAK,MAAM,IAAI,IAAI,wBAAwB,EAAE,CAAC;wCAC5C,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;wCAChD,IAAI,SAAS,EAAE,CAAC;4CACd,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;4CACtD,IAAI,SAAS,EAAE,CAAC;gDACd,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gDACzB,MAAM,KAAK,CAAC,gBAAgB,CAC1B,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACxC,EAAE,CACH,CAAC;4CACJ,CAAC;wCACH,CAAC;oCACH,CAAC;gCACH,CAAC;6BACF,CAAC,CAAC;wBACL,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,IAAI,cAAc,EAAE,CAAC;wBACnB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,aAAa;yBACzB,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,aAAa;4BACxB,GAAG,EAAE,KAAK,CAAC,EAAE;gCACX,MAAM,GAAG,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;gCACrC,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;gCAC3C,MAAM,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;gCACnD,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;gCAC3C,OAAO,KAAK,CAAC,WAAW,CACtB,UAAU,EACV,GAAG,GAAG,KAAK,MAAM,OAAO,UAAU,GAAG,SAAS,EAAE,CACjD,CAAC;4BACJ,CAAC;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;aACF,CAAC;YACF,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI;gBACvB,mBAAmB,CAAC,YAAY;oBAC9B,MAAM,QAAQ,GAAG,YAAY,CAAC,cAAc,EAAE,cAAc,CAAC;oBAC7D,IAAI,QAAQ,EAAE,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;wBACrD,OAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,YAAY;wBAClB,SAAS,EAAE,eAAe;wBAC1B,GAAG,EAAE,KAAK,CAAC,EAAE;4BACX,MAAM,GAAG,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;4BACvC,MAAM,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;4BACzC,MAAM,UAAU,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;4BACjD,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;4BAC7C,OAAO,KAAK,CAAC,WAAW,CACtB,YAAY,EACZ,GAAG,GAAG,GAAG,MAAM,KAAK,UAAU,GAAG,SAAS,EAAE,CAC7C,CAAC;wBACJ,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}