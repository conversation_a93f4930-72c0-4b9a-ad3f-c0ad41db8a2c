import { FoundationElement } from "../foundation-element/foundation-element.js";
/**
 * A Breadcrumb Custom HTML Element.
 * @slot - The default slot for the breadcrumb items
 * @csspart list - The element wrapping the slotted items
 *
 * @public
 */
export declare class Breadcrumb extends FoundationElement {
    /**
     * @internal
     */
    slottedBreadcrumbItems: HTMLElement[];
    slottedBreadcrumbItemsChanged(): void;
    private setItemSeparator;
    /**
     * Finds href on childnodes in the light DOM or shadow DOM.
     * We look in the shadow DOM because we insert an anchor when breadcrumb-item has an href.
     */
    private findChildWithHref;
    /**
     *  Sets ARIA Current for the current node
     * If child node with an anchor tag and with href is found then set aria-current to correct value for the child node,
     * otherwise apply aria-current to the host element, with an href
     */
    private setAriaCurrent;
}
